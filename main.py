# 这是一个示例 Python 脚本。

# 按 ⌃R 执行或将其替换为您的代码。
# 按 双击 ⇧ 在所有地方搜索类、文件、工具窗口、操作和设置。

import akshare as ak
import pandas as pd
from datetime import datetime
import os

def get_cn_mainland_index():
    """获取中国大陆A股重要指数数据"""
    print("正在获取沪深重要指数数据...")
    
    # 使用东方财富网的股票指数实时行情数据
    cn_index_df = ak.stock_zh_index_spot_em()
    
    # 筛选重要指数，如上证指数、深证成指、创业板指数、科创50等
    important_indices = ["上证指数", "深证成指", "创业板指", "科创50", "上证50", "沪深300", "中证500", "中证1000"]
    filtered_df = cn_index_df[cn_index_df["指数简称"].isin(important_indices)]
    
    return filtered_df

def get_hk_index():
    """获取香港重要指数数据"""
    print("正在获取港股重要指数数据...")
    
    # 使用东方财富网的港股指数实时行情数据
    hk_index_df = ak.stock_hk_index_spot_em()
    
    # 筛选重要指数，如恒生指数、国企指数等
    important_indices = ["恒生指数", "恒生国企", "恒生科技", "红筹指数"]
    filtered_df = hk_index_df[hk_index_df["指数名称"].isin(important_indices)]
    
    return filtered_df

def get_index_historical_data(index_code, start_date, end_date=None):
    """获取指定指数的历史数据"""
    if end_date is None:
        end_date = datetime.now().strftime("%Y%m%d")
        
    try:
        # 使用东方财富网的指数历史数据接口
        df = ak.index_zh_a_hist(symbol=index_code, start_date=start_date, end_date=end_date)
        return df
    except Exception as e:
        print(f"获取 {index_code} 历史数据失败: {e}")
        return None

def get_hk_index_historical_data(index_code, start_date, end_date=None):
    """获取港股指定指数的历史数据"""
    if end_date is None:
        end_date = datetime.now().strftime("%Y%m%d")
        
    try:
        # 使用东方财富网的港股指数历史数据接口
        df = ak.stock_hk_index_daily_em(symbol=index_code, start_date=start_date, end_date=end_date)
        return df
    except Exception as e:
        print(f"获取港股 {index_code} 历史数据失败: {e}")
        return None

def save_to_excel(dataframes, sheet_names, output_file):
    """将多个数据框保存到Excel文件中的不同表格"""
    with pd.ExcelWriter(output_file) as writer:
        for df, sheet_name in zip(dataframes, sheet_names):
            if df is not None and not df.empty:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                print(f"成功保存数据到表格: {sheet_name}")

def main():
    # 创建保存结果的目录
    output_dir = "index_data"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 获取当前日期作为文件名的一部分
    current_date = datetime.now().strftime("%Y%m%d")
    
    # 1. 获取并保存沪深重要指数实时数据
    cn_index_df = get_cn_mainland_index()
    cn_index_df.to_excel(f"{output_dir}/沪深重要指数_{current_date}.xlsx", index=False)
    print(f"沪深重要指数数据已保存到文件: 沪深重要指数_{current_date}.xlsx")
    
    # 2. 获取并保存港股重要指数实时数据
    hk_index_df = get_hk_index()
    hk_index_df.to_excel(f"{output_dir}/港股重要指数_{current_date}.xlsx", index=False)
    print(f"港股重要指数数据已保存到文件: 港股重要指数_{current_date}.xlsx")
    
    # 3. 获取指定指数的历史数据
    # 设置历史数据的开始日期（如最近一年的数据）
    start_date = "20240101"  # 可以根据需要调整
    
    # 沪深指数历史数据
    cn_index_hist_dfs = []
    cn_index_hist_sheet_names = []
    
    cn_index_codes = {
        "000001": "上证指数",
        "399001": "深证成指",
        "399006": "创业板指",
        "000688": "科创50",
        "000016": "上证50",
        "000300": "沪深300",
        "000905": "中证500",
        "000852": "中证1000"
    }
    
    for code, name in cn_index_codes.items():
        print(f"正在获取 {name} 的历史数据...")
        hist_df = get_index_historical_data(code, start_date)
        if hist_df is not None:
            cn_index_hist_dfs.append(hist_df)
            cn_index_hist_sheet_names.append(name)
    
    if cn_index_hist_dfs:
        save_to_excel(cn_index_hist_dfs, cn_index_hist_sheet_names, f"{output_dir}/沪深指数历史数据_{current_date}.xlsx")
    
    # 港股指数历史数据
    hk_index_hist_dfs = []
    hk_index_hist_sheet_names = []
    
    hk_index_codes = {
        "HSI": "恒生指数",
        "HSCEI": "恒生国企",
        "HSTECH": "恒生科技",
        "HSCCI": "红筹指数"
    }
    
    for code, name in hk_index_codes.items():
        print(f"正在获取 {name} 的历史数据...")
        hist_df = get_hk_index_historical_data(code, start_date)
        if hist_df is not None:
            hk_index_hist_dfs.append(hist_df)
            hk_index_hist_sheet_names.append(name)
    
    if hk_index_hist_dfs:
        save_to_excel(hk_index_hist_dfs, hk_index_hist_sheet_names, f"{output_dir}/港股指数历史数据_{current_date}.xlsx")
    
    print("所有指数数据获取完成！")

def print_hi(name):
    # 在下面的代码行中使用断点来调试脚本。
    print(f'Hi, {name}')  # 按 ⌘F8 切换断点。


# 按装订区域中的绿色按钮以运行脚本。
if __name__ == '__main__':
    main()

# 访问 https://www.jetbrains.com/help/pycharm/ 获取 PyCharm 帮助