#!/usr/bin/env python3
"""
数据库迁移脚本：从原有的DuckDB管理器迁移到SQLAlchemy版本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

from app.utils.db_manager import get_db_manager as get_old_db_manager
from app.utils.db_manager_new import get_db_manager as get_new_db_manager
from app.utils.logger_config import database_logger

logger = database_logger


def migrate_data():
    """迁移数据从旧数据库管理器到新的SQLAlchemy版本"""

    print("开始数据验证和兼容性检查...")
    logger.info("开始数据验证和兼容性检查")

    try:
        # 由于新旧系统都使用同一个数据库文件，我们只需要验证兼容性
        print("检查数据库兼容性...")
        check_database_compatibility()

        print("验证现有数据...")
        validate_existing_data()

        print("数据验证完成！新系统可以直接使用现有数据。")
        logger.info("数据验证完成")

    except Exception as e:
        print(f"数据验证失败: {e}")
        logger.error(f"数据验证失败: {e}")
        raise


def check_database_compatibility():
    """检查数据库兼容性"""
    try:
        # 使用新系统检查数据库结构
        new_db = get_new_db_manager()
        new_db.connect()

        # 检查表是否存在
        tables = new_db.execute_query("SHOW TABLES")
        table_names = [table['name'] for table in tables]

        expected_tables = ['favorite_stocks', 'favorite_reports', 'ai_stock_results', 'reports', 'report_stocks', 'report_operations']

        print(f"数据库中的表: {table_names}")

        for table in expected_tables:
            if table in table_names:
                print(f"✓ 表 {table} 存在")
            else:
                print(f"! 表 {table} 不存在，将自动创建")

        new_db.close()
        return True

    except Exception as e:
        print(f"检查数据库兼容性失败: {e}")
        return False


def validate_existing_data():
    """验证现有数据"""
    try:
        new_db = get_new_db_manager()
        new_db.connect()

        # 检查关注股票数据
        stocks = new_db.get_favorite_stocks()
        print(f"✓ 关注股票数据: {len(stocks)} 条记录")

        # 检查收藏研报数据
        reports = new_db.get_favorite_reports()
        print(f"✓ 收藏研报数据: {len(reports)} 条记录")

        # 检查研报数据
        report_count = new_db.get_reports_count()
        print(f"✓ 研报数据: {report_count} 条记录")

        # 检查统计信息
        stats = new_db.get_report_statistics()
        print(f"✓ 统计信息: {stats}")

        new_db.close()
        return True

    except Exception as e:
        print(f"验证现有数据失败: {e}")
        return False


def test_new_database():
    """测试新数据库功能"""
    print("\n测试新数据库功能...")

    try:
        db = get_new_db_manager()
        db.connect()

        # 测试关注股票功能
        print("测试关注股票功能...")
        original_count = len(db.get_favorite_stocks())

        # 添加测试数据
        test_success = db.add_favorite_stock("TEST_MIGRATE", "迁移测试股票", "测试用")
        if test_success:
            print("✓ 添加关注股票功能正常")
            # 清理测试数据
            db.remove_favorite_stock("TEST_MIGRATE")
        else:
            print("✗ 添加关注股票功能异常")

        # 测试收藏研报功能
        print("测试收藏研报功能...")
        original_report_count = len(db.get_favorite_reports())

        test_success = db.add_favorite_report("TEST_MIGRATE_REPORT", "迁移测试研报", "测试用")
        if test_success:
            print("✓ 收藏研报功能正常")
            # 清理测试数据
            db.remove_favorite_report("TEST_MIGRATE_REPORT")
        else:
            print("✗ 收藏研报功能异常")

        # 测试统计功能
        print("测试统计功能...")
        stats = db.get_report_statistics()
        print(f"✓ 统计功能正常，当前研报总数: {stats.get('total_count', 0)}")

        print("✓ 新数据库功能测试完成！")

    except Exception as e:
        print(f"✗ 测试新数据库功能失败: {e}")
        logger.error(f"测试新数据库功能失败: {e}")
    finally:
        if 'db' in locals():
            db.close()


def backup_old_database():
    """备份旧数据库文件"""
    try:
        from datetime import datetime
        
        old_db_path = PROJECT_ROOT / "data" / "database.duckdb"
        if old_db_path.exists():
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = PROJECT_ROOT / "data" / f"database_backup_{timestamp}.duckdb"
            
            import shutil
            shutil.copy2(old_db_path, backup_path)
            
            print(f"已备份旧数据库到: {backup_path}")
            logger.info(f"已备份旧数据库到: {backup_path}")
        else:
            print("未找到旧数据库文件，跳过备份")
            
    except Exception as e:
        print(f"备份数据库失败: {e}")
        logger.error(f"备份数据库失败: {e}")


if __name__ == "__main__":
    print("SQLAlchemy数据库兼容性检查工具")
    print("=" * 50)
    print("说明：新系统直接使用现有的DuckDB数据库文件，")
    print("     此工具用于验证数据兼容性和功能正常性。")
    print("=" * 50)

    # 备份旧数据库
    backup_old_database()

    # 执行兼容性检查
    migrate_data()

    # 测试新数据库
    test_new_database()

    print("\n✓ 兼容性检查完成！")
    print("新的SQLAlchemy系统可以直接使用现有数据。")
    print("请检查日志文件以获取详细信息。")
