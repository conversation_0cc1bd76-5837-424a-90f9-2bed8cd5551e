#!/usr/bin/env python3
"""
简单的数据库验证脚本
验证新的SQLAlchemy系统是否能正常工作
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试新数据库系统基本功能 ===")
    
    try:
        from app.utils.db_manager_new import get_db_manager
        
        # 获取数据库管理器
        db = get_db_manager()
        print("✓ 数据库管理器创建成功")
        
        # 连接数据库
        db.connect()
        print("✓ 数据库连接成功")
        
        # 测试关注股票功能
        print("\n--- 测试关注股票功能 ---")
        
        # 添加测试股票
        success = db.add_favorite_stock("TEST001", "测试股票", "验证用")
        print(f"添加关注股票: {'✓' if success else '✗'}")
        
        # 检查是否存在
        exists = db.is_stock_favorited("TEST001")
        print(f"检查股票存在: {'✓' if exists else '✗'}")
        
        # 获取列表
        stocks = db.get_favorite_stocks()
        print(f"获取关注股票列表: ✓ (共{len(stocks)}个)")
        
        # 删除测试股票
        success = db.remove_favorite_stock("TEST001")
        print(f"删除关注股票: {'✓' if success else '✗'}")
        
        # 测试收藏研报功能
        print("\n--- 测试收藏研报功能 ---")
        
        # 添加测试研报
        success = db.add_favorite_report("TEST_REPORT", "测试研报", "验证用")
        print(f"添加收藏研报: {'✓' if success else '✗'}")
        
        # 检查是否存在
        exists = db.is_report_favorited("TEST_REPORT")
        print(f"检查研报存在: {'✓' if exists else '✗'}")
        
        # 获取列表
        reports = db.get_favorite_reports()
        print(f"获取收藏研报列表: ✓ (共{len(reports)}个)")
        
        # 删除测试研报
        success = db.remove_favorite_report("TEST_REPORT")
        print(f"删除收藏研报: {'✓' if success else '✗'}")
        
        # 测试研报功能
        print("\n--- 测试研报功能 ---")
        
        # 获取研报统计
        stats = db.get_report_statistics()
        print(f"获取研报统计: ✓ (总数: {stats.get('total_count', 0)})")
        
        # 获取研报列表
        reports = db.get_reports(page=1, page_size=5)
        print(f"获取研报列表: ✓ (获取到{len(reports)}个)")
        
        # 关闭连接
        db.close()
        print("✓ 数据库连接关闭成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_service_layer():
    """测试服务层"""
    print("\n=== 测试服务层功能 ===")
    
    try:
        from app.database.services.database_service import DatabaseService
        
        # 测试关注股票
        success = DatabaseService.add_favorite_stock("SERVICE_TEST", "服务层测试", "测试")
        print(f"服务层添加关注股票: {'✓' if success else '✗'}")
        
        exists = DatabaseService.is_stock_favorited("SERVICE_TEST")
        print(f"服务层检查股票: {'✓' if exists else '✗'}")
        
        stocks = DatabaseService.get_favorite_stocks()
        print(f"服务层获取股票列表: ✓ (共{len(stocks)}个)")
        
        success = DatabaseService.remove_favorite_stock("SERVICE_TEST")
        print(f"服务层删除股票: {'✓' if success else '✗'}")
        
        return True
        
    except Exception as e:
        print(f"✗ 服务层测试失败: {e}")
        return False


def test_compatibility():
    """测试兼容性"""
    print("\n=== 测试向后兼容性 ===")
    
    try:
        # 测试是否可以像旧系统一样使用
        from app.utils.db_manager_new import get_db_manager
        
        db = get_db_manager()
        db.connect()
        
        # 测试旧接口是否仍然可用
        print("测试旧接口兼容性...")
        
        # ensure_connection 方法
        connected = db.ensure_connection()
        print(f"ensure_connection方法: {'✓' if connected else '✗'}")
        
        # execute_query 方法
        try:
            result = db.execute_query("SELECT 1 as test")
            print(f"execute_query方法: {'✓' if result else '✗'}")
        except Exception as e:
            print(f"execute_query方法: ✗ ({e})")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"✗ 兼容性测试失败: {e}")
        return False


def check_database_file():
    """检查数据库文件"""
    print("\n=== 检查数据库文件 ===")
    
    try:
        db_path = PROJECT_ROOT / "data" / "database.duckdb"
        
        if db_path.exists():
            size = db_path.stat().st_size
            print(f"✓ 数据库文件存在: {db_path}")
            print(f"✓ 文件大小: {size} 字节")
        else:
            print(f"! 数据库文件不存在，将自动创建: {db_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ 检查数据库文件失败: {e}")
        return False


def main():
    """主函数"""
    print("SQLAlchemy数据库系统验证")
    print("=" * 50)
    
    results = []
    
    # 检查数据库文件
    results.append(("数据库文件检查", check_database_file()))
    
    # 测试基本功能
    results.append(("基本功能测试", test_basic_functionality()))
    
    # 测试服务层
    results.append(("服务层测试", test_service_layer()))
    
    # 测试兼容性
    results.append(("兼容性测试", test_compatibility()))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("验证结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有验证都通过了！")
        print("新的SQLAlchemy数据库系统可以正常使用。")
        print("\n下一步:")
        print("1. 在您的代码中将导入改为: from app.utils.db_manager_new import get_db_manager")
        print("2. 运行您的应用程序进行实际测试")
        print("3. 确认一切正常后，可以删除旧的数据库管理器文件")
    else:
        print("\n⚠️  部分验证失败，请检查错误信息。")
        print("建议先解决这些问题再使用新系统。")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
