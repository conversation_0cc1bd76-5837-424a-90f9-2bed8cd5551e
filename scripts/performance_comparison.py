#!/usr/bin/env python3
"""
性能对比脚本：比较新旧数据库管理器的性能
"""
import sys
import os
import time
import uuid
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))


def benchmark_old_db():
    """测试旧数据库管理器性能"""
    print("测试旧数据库管理器性能...")
    
    try:
        from app.utils.db_manager import get_db_manager as get_old_db_manager
        
        db = get_old_db_manager()
        db.connect()
        
        # 测试添加关注股票
        start_time = time.time()
        for i in range(100):
            db.add_favorite_stock(f"old_test_{i:03d}", f"测试股票{i}", f"旧系统测试{i}")
        add_stock_time = time.time() - start_time
        
        # 测试查询关注股票
        start_time = time.time()
        for _ in range(10):
            stocks = db.get_favorite_stocks()
        query_stock_time = time.time() - start_time
        
        # 测试检查股票状态
        start_time = time.time()
        for i in range(100):
            db.is_stock_favorited(f"old_test_{i:03d}")
        check_stock_time = time.time() - start_time
        
        db.close()
        
        return {
            'add_stock_time': add_stock_time,
            'query_stock_time': query_stock_time,
            'check_stock_time': check_stock_time
        }
        
    except Exception as e:
        print(f"旧数据库测试失败: {e}")
        return None


def benchmark_new_db():
    """测试新数据库管理器性能"""
    print("测试新数据库管理器性能...")
    
    try:
        from app.utils.db_manager_new import get_db_manager as get_new_db_manager
        
        db = get_new_db_manager()
        db.connect()
        
        # 测试添加关注股票
        start_time = time.time()
        for i in range(100):
            db.add_favorite_stock(f"new_test_{i:03d}", f"测试股票{i}", f"新系统测试{i}")
        add_stock_time = time.time() - start_time
        
        # 测试查询关注股票
        start_time = time.time()
        for _ in range(10):
            stocks = db.get_favorite_stocks()
        query_stock_time = time.time() - start_time
        
        # 测试检查股票状态
        start_time = time.time()
        for i in range(100):
            db.is_stock_favorited(f"new_test_{i:03d}")
        check_stock_time = time.time() - start_time
        
        db.close()
        
        return {
            'add_stock_time': add_stock_time,
            'query_stock_time': query_stock_time,
            'check_stock_time': check_stock_time
        }
        
    except Exception as e:
        print(f"新数据库测试失败: {e}")
        return None


def benchmark_report_operations():
    """测试研报操作性能"""
    print("测试研报操作性能...")
    
    try:
        from app.utils.db_manager_new import get_db_manager
        
        db = get_db_manager()
        db.connect()
        
        # 准备测试数据
        test_reports = []
        for i in range(50):
            report_data = {
                'id': f'perf_report_{i:03d}',
                'title': f'性能测试研报 {i}',
                'content': f'这是第{i}个性能测试研报，内容较长' * 10,
                'label': 'performance_test',
                'create_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'code_list': [f'00000{i % 5}', f'00001{i % 3}'],
                'source': 'performance_test'
            }
            test_reports.append(report_data)
        
        # 测试插入研报
        start_time = time.time()
        for report_data in test_reports:
            db.insert_report(report_data)
        insert_time = time.time() - start_time
        
        # 测试查询研报
        start_time = time.time()
        for _ in range(10):
            reports = db.get_reports(page=1, page_size=20)
        query_time = time.time() - start_time
        
        # 测试搜索研报
        start_time = time.time()
        for _ in range(10):
            results = db.search_reports("性能测试", limit=10)
        search_time = time.time() - start_time
        
        # 测试更新评分
        start_time = time.time()
        for i in range(10):
            db.update_report_rating(f'perf_report_{i:03d}', (i % 10) + 1)
        update_time = time.time() - start_time
        
        db.close()
        
        return {
            'insert_time': insert_time,
            'query_time': query_time,
            'search_time': search_time,
            'update_time': update_time
        }
        
    except Exception as e:
        print(f"研报操作测试失败: {e}")
        return None


def cleanup_test_data():
    """清理测试数据"""
    print("清理测试数据...")
    
    try:
        # 清理新系统测试数据
        from app.utils.db_manager_new import get_db_manager as get_new_db_manager
        
        db = get_new_db_manager()
        db.connect()
        
        # 清理测试股票
        for i in range(100):
            db.remove_favorite_stock(f"new_test_{i:03d}")
        
        db.close()
        
        # 清理旧系统测试数据
        try:
            from app.utils.db_manager import get_db_manager as get_old_db_manager
            
            db = get_old_db_manager()
            db.connect()
            
            for i in range(100):
                db.remove_favorite_stock(f"old_test_{i:03d}")
            
            db.close()
        except:
            pass  # 旧系统可能不存在
        
        print("测试数据清理完成")
        
    except Exception as e:
        print(f"清理测试数据失败: {e}")


def print_comparison_results(old_results, new_results, report_results):
    """打印性能对比结果"""
    print("\n" + "=" * 60)
    print("性能对比结果")
    print("=" * 60)
    
    if old_results and new_results:
        print("\n股票操作性能对比:")
        print("-" * 40)
        
        operations = [
            ('添加100个关注股票', 'add_stock_time'),
            ('查询关注股票10次', 'query_stock_time'),
            ('检查股票状态100次', 'check_stock_time')
        ]
        
        for op_name, key in operations:
            old_time = old_results[key]
            new_time = new_results[key]
            improvement = ((old_time - new_time) / old_time) * 100 if old_time > 0 else 0
            
            print(f"{op_name}:")
            print(f"  旧系统: {old_time:.3f}秒")
            print(f"  新系统: {new_time:.3f}秒")
            print(f"  性能提升: {improvement:+.1f}%")
            print()
    
    if report_results:
        print("研报操作性能:")
        print("-" * 40)
        
        operations = [
            ('插入50个研报', 'insert_time'),
            ('查询研报10次', 'query_time'),
            ('搜索研报10次', 'search_time'),
            ('更新评分10次', 'update_time')
        ]
        
        for op_name, key in operations:
            time_taken = report_results[key]
            print(f"{op_name}: {time_taken:.3f}秒")
        
        print()
    
    print("总结:")
    print("-" * 40)
    if old_results and new_results:
        total_old = sum(old_results.values())
        total_new = sum(new_results.values())
        total_improvement = ((total_old - total_new) / total_old) * 100 if total_old > 0 else 0
        print(f"总体性能提升: {total_improvement:+.1f}%")
    
    print("✓ 新系统采用SQLAlchemy ORM，提供更好的类型安全和错误处理")
    print("✓ 连接池管理减少了连接开销")
    print("✓ 统一的异常处理和日志记录")
    print("✓ 模块化设计便于维护和扩展")


def main():
    """主函数"""
    print("数据库性能对比测试")
    print("=" * 60)
    
    # 运行性能测试
    old_results = benchmark_old_db()
    new_results = benchmark_new_db()
    report_results = benchmark_report_operations()
    
    # 打印结果
    print_comparison_results(old_results, new_results, report_results)
    
    # 清理测试数据
    cleanup_test_data()
    
    print("\n性能测试完成！")


if __name__ == "__main__":
    main()
