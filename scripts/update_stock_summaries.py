#!/usr/bin/env python3
"""
批量更新股票指标摘要脚本

用于为所有有指标数据但缺少摘要的股票生成摘要数据
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.strategy.services.offline_calculator import OfflineIndicatorCalculator
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def update_missing_summaries():
    """更新缺失的股票摘要"""
    calculator = OfflineIndicatorCalculator()

    try:
        # 确保数据库连接
        calculator._ensure_db_connection()

        # 获取所有有指标数据的股票
        stocks_with_indicators = calculator.conn.execute("""
            SELECT DISTINCT symbol
            FROM stock_indicators
            WHERE symbol NOT IN (SELECT symbol FROM indicator_summary)
            ORDER BY symbol
        """).fetchall()
        
        logger.info(f"发现 {len(stocks_with_indicators)} 只股票缺少摘要数据")
        
        success_count = 0
        for row in stocks_with_indicators:
            symbol = row[0]
            try:
                # 获取该股票的最新指标数据
                data = calculator.get_stock_indicators_from_db(symbol, limit=1)
                if not data.empty:
                    calculator._update_indicator_summary(symbol, data)
                    success_count += 1
                    logger.info(f"已更新 {symbol} 的摘要数据")
                else:
                    logger.warning(f"股票 {symbol} 无指标数据")
            except Exception as e:
                logger.error(f"更新股票 {symbol} 摘要失败: {e}")
        
        logger.info(f"批量更新完成，成功更新 {success_count} 只股票的摘要数据")
        
    except Exception as e:
        logger.error(f"批量更新失败: {e}")
    finally:
        calculator.close()

if __name__ == "__main__":
    update_missing_summaries()
