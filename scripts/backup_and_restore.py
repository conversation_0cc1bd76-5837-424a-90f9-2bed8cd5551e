#!/usr/bin/env python3
"""
数据库管理器备份和恢复脚本
"""
import os
import shutil
from pathlib import Path
from datetime import datetime

PROJECT_ROOT = Path(__file__).parent.parent.absolute()


def backup_db_manager():
    """备份当前的db_manager.py"""
    try:
        source = PROJECT_ROOT / "app" / "utils" / "db_manager.py"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"db_manager_backup_{timestamp}.py"
        backup_path = PROJECT_ROOT / "app" / "utils" / backup_name
        
        if source.exists():
            shutil.copy2(source, backup_path)
            print(f"✅ 备份成功: {backup_path}")
            return str(backup_path)
        else:
            print("❌ 源文件不存在")
            return None
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return None


def restore_db_manager(backup_path: str):
    """从备份恢复db_manager.py"""
    try:
        backup_file = Path(backup_path)
        target = PROJECT_ROOT / "app" / "utils" / "db_manager.py"
        
        if backup_file.exists():
            shutil.copy2(backup_file, target)
            print(f"✅ 恢复成功: 从 {backup_path} 恢复到 {target}")
            return True
        else:
            print("❌ 备份文件不存在")
            return False
    except Exception as e:
        print(f"❌ 恢复失败: {e}")
        return False


def list_backups():
    """列出所有备份文件"""
    try:
        utils_dir = PROJECT_ROOT / "app" / "utils"
        backups = list(utils_dir.glob("db_manager_backup_*.py"))
        
        if backups:
            print("📁 可用的备份文件:")
            for i, backup in enumerate(sorted(backups), 1):
                stat = backup.stat()
                mtime = datetime.fromtimestamp(stat.st_mtime)
                print(f"  {i}. {backup.name} (创建时间: {mtime.strftime('%Y-%m-%d %H:%M:%S')})")
            return backups
        else:
            print("📁 没有找到备份文件")
            return []
    except Exception as e:
        print(f"❌ 列出备份失败: {e}")
        return []


def interactive_restore():
    """交互式恢复"""
    backups = list_backups()
    if not backups:
        return
    
    try:
        choice = input("\n请选择要恢复的备份编号 (输入数字): ").strip()
        index = int(choice) - 1
        
        if 0 <= index < len(backups):
            backup_path = str(backups[index])
            confirm = input(f"确认要恢复 {backups[index].name} 吗? (y/N): ").strip().lower()
            
            if confirm in ['y', 'yes']:
                restore_db_manager(backup_path)
            else:
                print("❌ 取消恢复")
        else:
            print("❌ 无效的选择")
    except (ValueError, KeyboardInterrupt):
        print("❌ 取消恢复")


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("数据库管理器备份和恢复工具")
        print("=" * 40)
        print("用法:")
        print("  python scripts/backup_and_restore.py backup    # 创建备份")
        print("  python scripts/backup_and_restore.py restore   # 交互式恢复")
        print("  python scripts/backup_and_restore.py list      # 列出备份")
        return
    
    command = sys.argv[1].lower()
    
    if command == "backup":
        backup_path = backup_db_manager()
        if backup_path:
            print(f"\n💡 恢复命令:")
            print(f"python scripts/backup_and_restore.py restore")
    
    elif command == "restore":
        interactive_restore()
    
    elif command == "list":
        list_backups()
    
    else:
        print(f"❌ 未知命令: {command}")


if __name__ == "__main__":
    main()
