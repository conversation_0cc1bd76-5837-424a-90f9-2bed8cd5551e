#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
股票文本处理器测试脚本
"""

import sys
import os

# 添加项目根目录到模块搜索路径
project_root = os.path.abspath('.')
if project_root not in sys.path:
    sys.path.append(project_root)

# 配置日志：只输出到控制台，不生成文件
from app.utils.logger import set_log_config


from app.data_processors.common.stock_text_processor import StockTextProcessor

def test_stock_processor():
    """测试股票文本处理器"""
    
    # 创建处理器实例
    processor = StockTextProcessor()
    
    print("=== 股票文本处理器优化测试 ===")
    print(f"完整名称数量: {len(processor.full_names)}")
    print(f"简称数量: {len(processor.short_names)}")
    print(f"过滤词汇数量: {len(processor.short_names_filter)}")
    print()
    
    # 测试用例
    test_cases = [
        # 问题案例测试（应该不被识别）
        "小米和茅台如何？",
        "龙头股票表现不错",
        "华为和腾讯的竞争很激烈",
        "我喜欢吃小米粥",
        
        # 正确案例测试（应该被识别）
        "今天平安银行表现不错",
        "贵州茅台和小米集团继续走强",
        "阳光电源表现突出",
        "买入平安银行(000001)和贵州茅台（600519）",
        "关注000001这只股票",
        "今天股票市场中，小米集团表现突出",
    ]
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"测试 {i}: {test_text}")
        processed_text, code_list = processor.process_text_str(test_text)
        
        if code_list:
            print(f"✅ 识别到股票: {code_list}")
            print(f"处理结果: {processed_text}")
        else:
            print("❌ 未识别到股票")
        print("-" * 50)

if __name__ == '__main__':
    test_stock_processor()
