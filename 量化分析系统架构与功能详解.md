# 量化分析系统架构与功能详解

## 📋 系统概述

本系统是一个专业的股票量化分析平台，提供从数据采集到策略分析的完整解决方案。系统采用模块化设计，支持技术指标计算、K线形态识别、策略信号生成、定时任务调度等核心功能。

### 🎯 核心特性
- **数据采集与存储**: 基于akshare获取东方财富历史数据，按年月分区存储为parquet格式
- **技术指标计算**: 实现32种核心技术指标，包括MACD、KDJ、BOLL、RSI等
- **策略信号分析**: 多种买卖信号判断，支持KDJ、RSI、CCI、CR、WR、VR等策略
- **K线形态识别**: 自动识别16种经典K线形态，如吞噬形态、锤头线、十字星等
- **定时任务调度**: 每日复盘计算系统，自动更新数据和生成分析报告
- **RESTful API**: 完整的API接口，支持数据查询、指标计算、策略筛选等
- **高性能存储**: 基于DuckDB和Parquet的高效数据存储方案
- **Web界面**: 提供直观的Web界面进行数据查看和分析

## 🏗️ 系统架构

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    量化分析系统                              │
├─────────────────────────────────────────────────────────────┤
│  Web界面层 (Frontend)                                       │
│  ├── 股票列表页面    ├── 股票详情页面    ├── 形态筛选页面    │
│  ├── 指标排行页面    ├── 市场概览页面    ├── K线图组件       │
├─────────────────────────────────────────────────────────────┤
│  API接口层 (API Layer)                                      │
│  ├── 量化分析API     ├── 页面路由       ├── 策略API         │
│  ├── 数据服务API     ├── 指标服务API    ├── 市场数据API     │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层 (Business Layer)                                │
│  ├── 数据管理器      ├── 指标引擎       ├── 策略引擎        │
│  ├── 形态识别引擎    ├── 分析引擎       ├── 任务调度器      │
├─────────────────────────────────────────────────────────────┤
│  数据存储层 (Data Layer)                                    │
│  ├── DuckDB数据库    ├── Parquet文件    ├── 缓存系统        │
│  ├── 数据采集器      ├── 数据验证器     ├── 数据转换器      │
└─────────────────────────────────────────────────────────────┘
```

### 模块结构
```
app/strategy/
├── __init__.py              # 模块初始化
├── config.py                # 系统配置
├── README.md                # 说明文档
├── data/                    # 数据层
│   ├── collector.py         # 数据采集器
│   ├── manager.py           # 数据管理器
│   ├── validator.py         # 数据验证器
│   └── transformer.py       # 数据转换器
├── indicators/              # 指标层
│   └── engine.py            # 技术指标引擎
├── strategies/              # 策略层
│   └── engine.py            # 策略引擎
├── patterns/                # 形态层
│   └── engine.py            # 形态识别引擎
├── analysis/                # 分析层
│   └── engine.py            # 分析引擎
├── tasks/                   # 任务层
│   ├── scheduler.py         # 任务调度器
│   └── daily_review.py      # 每日复盘任务
├── api/                     # 接口层
│   ├── models.py            # 数据模型
│   └── routes.py            # API路由
├── services/                # 服务层
│   ├── data_service.py      # 数据服务
│   ├── indicator_service.py # 指标服务
│   └── market_data_service.py # 市场数据服务
├── utils/                   # 工具层
├── tests/                   # 测试
└── examples/                # 示例
```

## 🔧 核心模块详解

### 1. 数据层 (Data Layer)

#### DataCollector - 数据采集器
**功能**: 负责从外部数据源采集股票数据
**核心方法**:
- `get_stock_list()`: 获取A股股票列表
- `get_stock_daily_data(symbol, start_date, end_date)`: 获取股票日线数据
- `collect_stock_data(symbol)`: 采集单只股票数据
- `collect_all_stocks_data()`: 批量采集所有股票数据
- `smart_gap_filling(symbol)`: 智能数据缺口填补

**特性**:
- 支持断点续传
- 并发采集提升效率
- 自动去重避免重复数据
- 智能重试机制

#### DataManager - 数据管理器
**功能**: 管理数据的存储、读取和查询
**核心方法**:
- `load_stock_data(symbol)`: 加载股票数据
- `save_stock_data(symbol, data)`: 保存股票数据
- `get_available_symbols()`: 获取可用股票列表
- `get_date_range(symbol)`: 获取数据日期范围

**存储策略**:
- 按年月分区存储为Parquet格式
- 基于DuckDB的高性能查询
- 支持增量更新和历史数据管理

### 2. 指标层 (Indicators Layer)

#### IndicatorEngine - 技术指标引擎
**功能**: 计算各种技术指标
**支持的指标** (32种):

**趋势指标**:
- MACD (指数平滑移动平均线)
- BOLL (布林带)
- TRIX (三重指数平滑移动平均)
- SMA/EMA (简单/指数移动平均)
- DMA (动态移动平均)
- AMA (自适应移动平均)

**震荡指标**:
- RSI (相对强弱指标)
- KDJ (随机指标)
- CCI (顺势指标)
- WR (威廉指标)
- ROC (变动率指标)
- PSY (心理线指标)
- BIAS (乖离率)

**成交量指标**:
- OBV (能量潮指标)
- VR (成交量比率)
- MAVR (移动平均成交量比)
- MFI (资金流量指标)
- VWMA (成交量加权移动平均)

**动量指标**:
- DMI (动向指标)
- +DI/-DI (正负动向指标)
- DX/ADX/ADXR (趋向指标)

**波动指标**:
- TR/ATR (真实波幅)
- SAR (抛物线指标)
- BRAR (人气意愿指标)
- EMV (简易波动指标)

**其他指标**:
- CR (中间意愿指标)
- TEMA (三重指数移动平均)
- PPO (价格震荡百分比)
- WT (威廉指标)
- Supertrend (超级趋势)
- DPO (区间震荡线)
- VHF (垂直水平过滤器)
- RVI (相对活力指标)
- FI (强力指标)
- ENE (轨道线)
- STOCHRSI (随机RSI)

### 3. 策略层 (Strategies Layer)

#### StrategyEngine - 策略引擎
**功能**: 基于技术指标生成买卖信号
**支持的策略**:
- **KDJ策略**: 基于KDJ指标的金叉死叉信号
- **RSI策略**: 基于RSI超买超卖信号
- **CCI策略**: 基于CCI顺势指标信号
- **CR策略**: 基于CR中间意愿指标信号
- **WR策略**: 基于威廉指标信号
- **VR策略**: 基于成交量比率信号

**信号类型**:
- BUY (买入)
- SELL (卖出)
- HOLD (持有)

**信号强度**: 0.0-1.0，表示信号的可信度

### 4. 形态层 (Patterns Layer)

#### PatternEngine - 形态识别引擎
**功能**: 自动识别K线形态
**支持的形态** (16种):

**看涨形态**:
- 看涨吞没 (Bullish Engulfing)
- 锤头线 (Hammer)
- 蜻蜓十字 (Dragonfly Doji)
- 晨星 (Morning Star)
- 三个白兵 (Three White Soldiers)
- 刺透形态 (Piercing Pattern)
- 倒锤头 (Inverted Hammer)

**看跌形态**:
- 看跌吞没 (Bearish Engulfing)
- 射击之星 (Shooting Star)
- 墓碑十字 (Gravestone Doji)
- 暮星 (Evening Star)
- 三只乌鸦 (Three Black Crows)
- 乌云压顶 (Dark Cloud Cover)
- 上吊线 (Hanging Man)

**中性形态**:
- 十字星 (Doji)
- 母子线 (Harami)

**形态特征**:
- 置信度评分 (0.0-1.0)
- 信号类型 (BULLISH/BEARISH/NEUTRAL)
- 形态描述和出现日期

## 📡 API接口详解

### 量化分析API (/api/quantitative)

#### 市场数据接口
```http
GET /api/quantitative/market/overview
# 获取市场概览

GET /api/quantitative/market/sectors?limit=20
# 获取板块数据

GET /api/quantitative/market/industries?limit=20
# 获取行业数据

GET /api/quantitative/market/indices
# 获取主要指数数据
```

#### 股票数据接口
```http
GET /api/quantitative/stocks/list?page=1&size=50&sort_by=amount&search=keyword
# 获取股票列表
# 参数:
# - page: 页码 (默认1)
# - size: 每页数量 (默认50, 最大500)
# - sort_by: 排序字段 (amount/volume/pct_change)
# - search: 搜索关键词

GET /api/quantitative/stocks/{symbol}/basic
# 获取股票基本信息

GET /api/quantitative/stocks/{symbol}/kline?start_date=2024-01-01&end_date=2024-12-31&limit=250
# 获取股票K线数据
# 参数:
# - start_date: 开始日期 (YYYY-MM-DD)
# - end_date: 结束日期 (YYYY-MM-DD)
# - limit: 数据条数 (1-1000)

GET /api/quantitative/stocks/{symbol}/indicators?indicators=MACD,RSI,KDJ&start_date=2024-01-01
# 获取股票技术指标
# 参数:
# - indicators: 指标列表，逗号分隔 (默认: MACD,RSI,KDJ,BOLL,MA)
# - start_date: 开始日期
# - end_date: 结束日期

GET /api/quantitative/stocks/{symbol}/summary
# 获取股票指标摘要

GET /api/quantitative/stocks/{symbol}/patterns
# 获取股票K线形态识别结果
```

#### 指标排行接口
```http
GET /api/quantitative/indicators/ranking?indicator=RSI&limit=50
# 获取指标排行榜
# 参数:
# - indicator: 指标名称 (默认RSI)
# - limit: 返回数量 (1-200, 默认50)

GET /api/quantitative/indicators/available
# 获取可用指标列表
```

#### 形态筛选接口
```http
GET /api/quantitative/patterns/screening?signal_type=BULLISH&min_confidence=0.6&pattern_types=engulfing,hammer&limit=50
# 形态筛选
# 参数:
# - signal_type: 信号类型 (BULLISH/BEARISH/NEUTRAL)
# - min_confidence: 最小置信度 (0.0-1.0, 默认0.6)
# - pattern_types: 形态类型，逗号分隔
# - limit: 返回数量 (1-200, 默认50)
```

#### 批量计算接口
```http
POST /api/quantitative/stocks/batch/indicators
Content-Type: application/json

{
  "symbols": ["000001", "000002", "000858"],
  "indicators": ["MACD", "RSI", "KDJ"]
}
# 批量计算股票指标
```

### 策略分析API (/api/strategy)

#### 核心接口
```http
GET /api/strategy/health
# 健康检查

GET /api/strategy/stocks
# 获取股票列表

GET /api/strategy/stocks/{symbol}/data?start_date=2024-01-01&limit=100
# 获取股票数据

GET /api/strategy/stocks/{symbol}/indicators
# 获取技术指标

GET /api/strategy/stocks/{symbol}/signals
# 获取策略信号

GET /api/strategy/stocks/{symbol}/patterns
# 获取K线形态

POST /api/strategy/screening
Content-Type: application/json

{
  "min_strength": 0.6,
  "signal_type": "BUY",
  "limit": 50
}
# 股票筛选

GET /api/strategy/market/overview
# 市场概览

GET /api/strategy/reports/daily
# 每日复盘报告
```

## 🖥️ Web界面功能

### 页面路由

#### 量化分析页面 (/quantitative)
```http
GET /quantitative/stocks
# 股票列表页面
# 功能: 展示股票列表、搜索、排序、分页

GET /quantitative/stocks/{symbol}
# 股票详情页面
# 功能: K线图、技术指标、形态识别、实时数据

GET /quantitative/indicators
# 指标排行页面
# 功能: 各种技术指标的排行榜、筛选

GET /quantitative/patterns
# 形态筛选页面
# 功能: K线形态筛选、条件设置、结果展示

GET /quantitative/market
# 市场概览页面
# 功能: 市场整体情况、热门股票、板块数据
```

### 核心组件

#### 1. 股票列表页面
**功能特性**:
- 股票搜索和筛选
- 多字段排序 (成交额、涨跌幅、成交量)
- 分页显示
- 实时价格更新
- 快速跳转到股票详情

**技术实现**:
- 响应式设计，支持移动端
- 异步数据加载
- 防抖搜索优化
- 虚拟滚动优化大数据量显示

#### 2. 股票详情页面
**功能特性**:
- **高性能K线图**: 基于Lightweight Charts，支持缩放、平移
- **技术指标展示**: 多种指标的图表和数值显示
- **形态识别结果**: 最新形态和历史形态切换查看
- **实时指标**: 当前RSI、MACD等关键指标
- **指标详情**: 移动平均线、KDJ、布林带等详细数据

**K线图特性**:
- 默认显示120天数据
- 支持30/60/120/250天切换
- 成交量柱状图
- 技术指标叠加显示
- 十字光标数据提示

**形态识别特性**:
- 默认显示最近7天形态
- 支持查看全部历史形态
- 形态置信度和信号类型显示
- 颜色编码区分看涨/看跌/中性

#### 3. 形态筛选页面
**功能特性**:
- **筛选条件设置**:
  - 信号类型选择 (看涨/看跌/中性)
  - 最小置信度设置 (50%-90%)
  - 形态类型多选 (16种经典形态)
  - 返回数量限制
- **结果展示**:
  - 卡片式股票展示
  - 形态标签和置信度
  - 最新价格和涨跌幅
  - 点击查看详情

**UI优化**:
- 按钮组选择替代下拉框
- 响应式网格布局
- 加载状态和进度提示
- 错误处理和重试机制

#### 4. 指标排行页面
**功能特性**:
- 多种技术指标排行榜
- 指标说明和使用建议
- 超买超卖区域标识
- 历史数据对比

#### 5. 市场概览页面
**功能特性**:
- 市场整体统计
- 热门股票展示
- 板块和行业数据
- 市场情绪分析

### 性能优化

#### 前端优化
- **缓存策略**: 数据缓存和组件缓存
- **懒加载**: 图片和组件按需加载
- **防抖节流**: 搜索和滚动事件优化
- **虚拟滚动**: 大数据量列表优化

#### 后端优化
- **数据缓存**: LRU缓存形态识别结果
- **查询优化**: 限制数据范围到最近30天
- **并发控制**: 限制同时分析的股票数量
- **批量处理**: 批量计算和数据传输

## 🔄 数据流程

### 数据采集流程
```
1. 获取股票列表 → 2. 并发采集数据 → 3. 数据验证 → 4. 格式转换 → 5. 分区存储
```

### 指标计算流程
```
1. 加载股票数据 → 2. 数据预处理 → 3. 指标计算 → 4. 结果缓存 → 5. 返回结果
```

### 形态识别流程
```
1. 获取K线数据 → 2. 形态检测 → 3. 置信度评估 → 4. 信号分类 → 5. 结果输出
```

### 策略分析流程
```
1. 技术指标计算 → 2. 多策略分析 → 3. 信号综合 → 4. 强度评估 → 5. 决策输出
```

## 🚀 部署和使用

### 环境要求
- Python 3.8+
- 主要依赖: pandas, numpy, talib, akshare, duckdb, fastapi

### 快速启动
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动Web服务
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 3. 访问系统
http://localhost:8000/quantitative/stocks
```

### 数据初始化
```bash
# 采集样本数据
python app/strategy/examples/collect_sample_data.py

# 计算技术指标
python app/strategy/examples/calculate_indicators.py

# 运行完整演示
python app/strategy/examples/complete_demo.py
```

## 📈 应用场景

1. **个人投资**: 技术分析、选股筛选、投资决策支持
2. **量化研究**: 策略回测、因子分析、模型验证
3. **机构应用**: 风险管理、投资组合优化、自动化交易
4. **教育培训**: 量化投资教学、技术分析学习

## 🔮 扩展开发

### 添加新技术指标
```python
def _calculate_custom_indicator(self, data: pd.DataFrame) -> pd.Series:
    """自定义指标计算"""
    # 实现指标计算逻辑
    return custom_values
```

### 添加新策略
```python
def _custom_strategy(self, data: pd.DataFrame) -> Dict[str, Any]:
    """自定义策略"""
    return {
        'signal': 'BUY',
        'strength': 0.8,
        'reason': '自定义策略信号'
    }
```

### 添加新形态
```python
def _detect_custom_pattern(self, data: pd.DataFrame) -> List[Dict[str, Any]]:
    """自定义形态识别"""
    # 实现形态识别逻辑
    return detections
```

## 📊 数据模型详解

### 股票数据结构
```python
{
    "symbol": "000001",           # 股票代码
    "name": "平安银行",           # 股票名称
    "date": "2024-01-15",        # 交易日期
    "open": 12.50,               # 开盘价
    "high": 12.80,               # 最高价
    "low": 12.30,                # 最低价
    "close": 12.65,              # 收盘价
    "volume": 1500000,           # 成交量
    "amount": 18975000.0,        # 成交额
    "pct_change": 1.20,          # 涨跌幅(%)
    "change": 0.15               # 涨跌额
}
```

### 技术指标数据结构
```python
{
    "symbol": "000001",
    "date": "2024-01-15",
    "indicators": {
        "MACD": {
            "dif": 0.15,         # DIF值
            "dea": 0.12,         # DEA值
            "macd": 0.06         # MACD柱状图
        },
        "RSI": {
            "rsi": 65.5,         # RSI值
            "rsi_signal": "NEUTRAL"  # RSI信号
        },
        "KDJ": {
            "k": 75.2,           # K值
            "d": 68.9,           # D值
            "j": 87.8            # J值
        },
        "BOLL": {
            "upper": 13.20,      # 上轨
            "middle": 12.65,     # 中轨(MA20)
            "lower": 12.10       # 下轨
        }
    }
}
```

### 形态识别数据结构
```python
{
    "symbol": "000001",
    "patterns": [
        {
            "pattern_name": "hammer",
            "pattern_type": "reversal",
            "signal": "BULLISH",
            "confidence": 0.85,
            "date": "2024-01-15",
            "description": "锤头线 - 看涨反转信号"
        }
    ],
    "summary": {
        "BULLISH": 3,
        "BEARISH": 1,
        "NEUTRAL": 2
    },
    "total": 6
}
```

### 策略信号数据结构
```python
{
    "symbol": "000001",
    "signals": {
        "kdj_strategy": {
            "signal": "BUY",
            "strength": 0.75,
            "reason": "KDJ金叉，K值上穿D值"
        },
        "rsi_strategy": {
            "signal": "HOLD",
            "strength": 0.45,
            "reason": "RSI处于中性区域"
        }
    },
    "comprehensive_signal": {
        "signal": "BUY",
        "strength": 0.68,
        "reason": "多个策略看涨信号"
    }
}
```

## 🛠️ 配置管理

### 系统配置 (config.py)
```python
@dataclass
class DataConfig:
    """数据配置"""
    data_root: str = "data/strategy"
    database_path: str = "data/strategy/strategy.duckdb"
    akshare_timeout: int = 30
    max_retries: int = 3
    update_start_date: str = "2023-01-01"
    batch_size: int = 100

@dataclass
class IndicatorConfig:
    """技术指标配置"""
    macd_params: Dict = field(default_factory=lambda: {"fast": 12, "slow": 26, "signal": 9})
    kdj_params: Dict = field(default_factory=lambda: {"k_period": 9, "d_period": 3})
    boll_params: Dict = field(default_factory=lambda: {"period": 20, "std": 2})
    rsi_params: Dict = field(default_factory=lambda: {"period": 14})
    enable_cache: bool = True
    cache_ttl: int = 3600

@dataclass
class StrategyConfig:
    """策略配置"""
    rsi_overbought: float = 80
    rsi_oversold: float = 20
    cci_overbought: float = 100
    cci_oversold: float = -100
    kdj_overbought: Dict = field(default_factory=lambda: {"K": 80, "D": 70})
    kdj_oversold: Dict = field(default_factory=lambda: {"K": 20, "D": 30})

@dataclass
class PatternConfig:
    """形态识别配置"""
    min_pattern_length: int = 2
    max_pattern_length: int = 5
    tolerance: float = 0.01
    supported_patterns: List[str] = field(default_factory=lambda: [
        "engulfing", "hammer", "shooting_star", "doji",
        "morning_star", "evening_star", "three_white_soldiers"
    ])
```

## 🔍 错误处理和日志

### 错误处理策略
```python
# API错误响应格式
{
    "success": false,
    "error": "数据获取失败",
    "message": "股票代码不存在或网络异常",
    "timestamp": "2024-01-15T10:30:00Z",
    "error_code": "DATA_NOT_FOUND"
}

# 常见错误类型
- DATA_NOT_FOUND: 数据不存在
- NETWORK_ERROR: 网络连接异常
- CALCULATION_ERROR: 计算过程错误
- VALIDATION_ERROR: 数据验证失败
- RATE_LIMIT_EXCEEDED: 请求频率超限
```

### 日志配置
```python
# 日志级别和格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/strategy.log'),
        logging.StreamHandler()
    ]
)

# 关键日志记录点
- 数据采集开始/结束
- 指标计算成功/失败
- 形态识别结果
- API请求和响应
- 系统性能指标
```

## 📈 性能监控

### 系统性能指标
```python
# 缓存命中率监控
{
    "data_cache": {
        "hits": 1250,
        "misses": 180,
        "hit_rate": 0.874
    },
    "indicator_cache": {
        "hits": 890,
        "misses": 45,
        "hit_rate": 0.952
    }
}

# API响应时间监控
{
    "endpoints": {
        "/api/quantitative/stocks/list": {
            "avg_response_time": 120,  # ms
            "max_response_time": 350,
            "min_response_time": 80
        },
        "/api/quantitative/patterns/screening": {
            "avg_response_time": 2500,
            "max_response_time": 5000,
            "min_response_time": 1200
        }
    }
}
```

### 数据质量监控
```python
# 数据完整性检查
{
    "data_quality": {
        "total_stocks": 5000,
        "stocks_with_data": 4850,
        "data_coverage": 0.97,
        "latest_update": "2024-01-15T18:00:00Z",
        "missing_data_stocks": ["000123", "000456"]
    }
}
```

## 🔐 安全和权限

### API安全
- **请求频率限制**: 每分钟100次请求
- **数据访问控制**: 基于IP和用户的访问限制
- **输入验证**: 严格的参数验证和SQL注入防护
- **错误信息脱敏**: 避免敏感信息泄露

### 数据安全
- **数据备份**: 定期自动备份重要数据
- **访问日志**: 记录所有数据访问操作
- **数据加密**: 敏感配置信息加密存储

## 🧪 测试策略

### 单元测试
```python
# 数据采集测试
def test_data_collector():
    collector = DataCollector()
    data = collector.get_stock_daily_data("000001", "2024-01-01", "2024-01-31")
    assert not data.empty
    assert "close" in data.columns

# 指标计算测试
def test_indicator_calculation():
    engine = IndicatorEngine()
    data = load_test_data()
    indicators = engine.calculate_macd(data)
    assert "dif" in indicators
    assert "dea" in indicators

# 形态识别测试
def test_pattern_recognition():
    engine = PatternEngine()
    data = load_test_data()
    patterns = engine.detect_hammer(data)
    assert isinstance(patterns, list)
```

### 集成测试
```python
# 完整流程测试
def test_complete_workflow():
    # 1. 数据采集
    collector = DataCollector()
    success = collector.collect_stock_data("000001")
    assert success

    # 2. 指标计算
    indicator_engine = IndicatorEngine()
    data = DataManager().load_stock_data("000001")
    indicators = indicator_engine.calculate_all_indicators(data)
    assert not indicators.empty

    # 3. 策略分析
    strategy_engine = StrategyEngine()
    signals = strategy_engine.analyze_signals(indicators)
    assert "kdj_strategy" in signals

    # 4. 形态识别
    pattern_engine = PatternEngine()
    patterns = pattern_engine.detect_all_patterns(data)
    assert isinstance(patterns, dict)
```

### 性能测试
```python
# 并发测试
def test_concurrent_requests():
    import concurrent.futures

    def make_request(symbol):
        response = requests.get(f"/api/quantitative/stocks/{symbol}/indicators")
        return response.status_code == 200

    symbols = ["000001", "000002", "000858", "002415", "300502"]

    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        results = list(executor.map(make_request, symbols))

    assert all(results)

# 大数据量测试
def test_large_dataset():
    # 测试处理大量股票数据的性能
    symbols = get_all_stock_symbols()[:1000]  # 1000只股票

    start_time = time.time()
    results = batch_calculate_indicators(symbols)
    end_time = time.time()

    assert len(results) == 1000
    assert (end_time - start_time) < 300  # 5分钟内完成
```

## 📚 开发指南

### 添加新功能的步骤

#### 1. 添加新技术指标
```python
# 1. 在 indicators/engine.py 中添加计算方法
def _calculate_new_indicator(self, data: pd.DataFrame) -> pd.Series:
    """新指标计算"""
    # 实现计算逻辑
    return result

# 2. 在 calculate_all_indicators 中注册
self.indicators['NEW_INDICATOR'] = self._calculate_new_indicator

# 3. 添加配置参数
@dataclass
class IndicatorConfig:
    new_indicator_params: Dict = field(default_factory=lambda: {"period": 14})

# 4. 编写测试
def test_new_indicator():
    engine = IndicatorEngine()
    data = load_test_data()
    result = engine._calculate_new_indicator(data)
    assert not result.empty
```

#### 2. 添加新策略
```python
# 1. 在 strategies/engine.py 中添加策略方法
def _new_strategy(self, data: pd.DataFrame) -> Dict[str, Any]:
    """新策略"""
    # 实现策略逻辑
    return {
        'signal': 'BUY',
        'strength': 0.8,
        'reason': '新策略信号'
    }

# 2. 在构造函数中注册
self.strategies['new_strategy'] = self._new_strategy

# 3. 添加配置参数
@dataclass
class StrategyConfig:
    new_strategy_threshold: float = 0.6
```

#### 3. 添加新形态
```python
# 1. 在 patterns/engine.py 中添加识别方法
def _detect_new_pattern(self, data: pd.DataFrame) -> List[Dict[str, Any]]:
    """新形态识别"""
    detections = []
    # 实现识别逻辑
    return detections

# 2. 在 detect_all_patterns 中注册
patterns['new_pattern'] = self._detect_new_pattern(data)

# 3. 更新配置
@dataclass
class PatternConfig:
    supported_patterns: List[str] = field(default_factory=lambda: [
        "engulfing", "hammer", "new_pattern"  # 添加新形态
    ])
```

### 代码规范

#### Python代码规范
```python
# 1. 使用类型注解
def calculate_indicator(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
    """计算指标"""
    pass

# 2. 文档字符串
def get_stock_data(self, symbol: str) -> pd.DataFrame:
    """获取股票数据

    Args:
        symbol: 股票代码

    Returns:
        股票数据DataFrame

    Raises:
        ValueError: 当股票代码无效时
    """
    pass

# 3. 错误处理
try:
    data = self.get_stock_data(symbol)
except Exception as e:
    logger.error(f"获取股票 {symbol} 数据失败: {e}")
    raise

# 4. 配置管理
from app.strategy.config import settings
timeout = settings.data.akshare_timeout
```

#### API设计规范
```python
# 1. 统一响应格式
{
    "success": true,
    "data": {...},
    "message": "操作成功",
    "timestamp": "2024-01-15T10:30:00Z"
}

# 2. 错误处理
@router.get("/stocks/{symbol}")
async def get_stock(symbol: str):
    try:
        data = service.get_stock_data(symbol)
        return JSONResponse(content={"success": True, "data": data})
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"获取股票数据失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

# 3. 参数验证
@router.get("/stocks")
async def get_stocks(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(50, ge=1, le=500, description="每页数量")
):
    pass
```

---

*本文档详细介绍了量化分析系统的架构设计、功能模块、API接口、Web界面、数据模型、配置管理、错误处理、性能监控、安全策略、测试方法和开发指南，为开发者和用户提供全面的技术参考和实践指导。*
