fastapi==0.104.1
uvicorn==0.23.2
jinja2==3.1.2
python-multipart==0.0.6
akshare==1.11.20
pydantic==2.4.2
duckdb==0.9.0
httpx==0.25.0
requests==2.31.0
openai==1.3.0
anthropic==0.7.0
google-generativeai==0.3.0
python-dotenv==1.0.0

# SQLAlchemy 相关依赖
sqlalchemy==2.0.23
sqlalchemy-duckdb==0.9.0

# 量化分析依赖
akshare==1.11.20
pandas==2.1.3
numpy==1.24.3
talib==0.4.28
tqdm==4.66.1

# 数据存储和处理
pyarrow==14.0.1  # parquet文件支持
polars==0.19.12  # 高性能数据处理（可选）

# 科学计算
scipy==1.11.4
scikit-learn==1.3.2

# 时间序列分析
statsmodels==0.14.0

# 可视化（可选，用于调试）
matplotlib==3.8.2
plotly==5.17.0

# 任务调度
schedule==1.2.0
celery==5.3.4  # 异步任务队列（可选）

# 性能监控
psutil==5.9.6