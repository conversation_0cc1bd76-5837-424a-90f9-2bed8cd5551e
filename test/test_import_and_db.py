#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证研报数据导入和数据库功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

from app.utils.tmp.db_manager_report import DuckDBManager
from app.scripts.zsxq_data_loader_db import get_data_loader


def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===")
    try:
        db_manager = DuckDBManager()
        db_manager.connect()
        print("✓ 数据库连接成功")
        
        # 检查表是否存在
        tables = db_manager.execute_query("SHOW TABLES")
        print(f"✓ 数据库中的表: {[table['name'] for table in tables]}")
        
        db_manager.close()
        return True
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False

def test_data_loader():
    """测试数据加载器"""
    print("\n=== 测试数据加载器 ===")
    try:
        loader = get_data_loader()
        
        # 测试获取研报总数
        total_count = loader.get_total_count()
        print(f"✓ 数据库中的研报总数: {total_count}")
        
        # 测试获取研报列表
        reports = loader.get_reports(page=1, page_size=5)
        print(f"✓ 获取研报列表成功，返回 {len(reports)} 条记录")
        
        if reports:
            first_report = reports[0]
            print(f"  - 第一条研报ID: {first_report.Id}")
            print(f"  - 标题: {first_report.Title[:50]}...")
            print(f"  - 评分: {getattr(first_report, 'rating', 0)}")
            print(f"  - 是否删除: {getattr(first_report, 'is_deleted', False)}")
        
        # 测试统计信息
        stats = loader.get_statistics()
        print(f"✓ 统计信息: {stats}")
        
        return True
    except Exception as e:
        print(f"✗ 数据加载器测试失败: {e}")
        return False

def test_report_management():
    """测试研报管理功能"""
    print("\n=== 测试研报管理功能 ===")
    try:
        loader = get_data_loader(read_only=False)
        
        # 获取第一个研报进行测试
        reports = loader.get_reports(page=1, page_size=1)
        if not reports:
            print("✗ 没有研报可以测试")
            return False
        
        test_report = reports[0]
        report_id = str(test_report.Id)
        print(f"✓ 测试研报ID: {report_id}")
        
        # 测试评分功能
        print("  测试评分功能...")
        original_rating = getattr(test_report, 'rating', 0)
        new_rating = 8 if original_rating != 8 else 7
        
        success = loader.update_report_rating(report_id, new_rating)
        if success:
            print(f"  ✓ 评分更新成功: {original_rating} -> {new_rating}")
            
            # 恢复原评分
            loader.update_report_rating(report_id, original_rating)
            print(f"  ✓ 评分恢复成功: {new_rating} -> {original_rating}")
        else:
            print("  ✗ 评分更新失败")
        
        # 测试标记删除功能（只测试，不实际删除）
        print("  测试删除状态查询...")
        report_detail = loader.get_report_by_id(report_id, include_deleted=True)
        is_deleted = getattr(report_detail, 'is_deleted', False)
        print(f"  ✓ 当前删除状态: {is_deleted}")
        
        return True
    except Exception as e:
        print(f"✗ 研报管理功能测试失败: {e}")
        return False

def test_search_functionality():
    """测试搜索功能"""
    print("\n=== 测试搜索功能 ===")
    try:
        loader = get_data_loader()
        
        # 测试标签筛选
        labels = loader.get_all_labels()
        print(f"✓ 可用标签: {labels[:5]}..." if len(labels) > 5 else f"✓ 可用标签: {labels}")
        
        if labels:
            # 使用第一个标签进行筛选测试
            test_label = labels[0]
            filtered_reports = loader.get_reports(page=1, page_size=3, label=test_label)
            print(f"✓ 标签'{test_label}'筛选结果: {len(filtered_reports)} 条")
        
        # 测试日期筛选
        dates = loader.get_all_dates()
        print(f"✓ 可用日期: {dates[:3]}..." if len(dates) > 3 else f"✓ 可用日期: {dates}")
        
        # 测试全文搜索
        search_results = loader.search_reports("市场", limit=3)
        print(f"✓ 搜索'市场'结果: {len(search_results)} 条")
        
        return True
    except Exception as e:
        print(f"✗ 搜索功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试研报数据库系统...")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("数据库连接", test_database_connection()))
    test_results.append(("数据加载器", test_data_loader()))
    test_results.append(("研报管理功能", test_report_management()))
    test_results.append(("搜索功能", test_search_functionality()))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print("="*50)
    print(f"测试完成: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试均通过！系统功能正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查系统配置。")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 