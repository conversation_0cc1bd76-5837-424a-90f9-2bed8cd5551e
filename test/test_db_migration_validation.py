#!/usr/bin/env python3
"""
数据库迁移升级验证测试脚本
全面测试新旧系统的功能对比和数据一致性
"""
import sys
import os
import uuid
from pathlib import Path
from datetime import datetime
import time

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))


class DatabaseMigrationValidator:
    """数据库迁移验证器"""
    
    def __init__(self):
        self.test_results = []
        self.old_db = None
        self.new_db = None
        
    def setup_databases(self):
        """设置数据库连接"""
        try:
            # 导入新系统
            from app.utils.db_manager_new import get_db_manager as get_new_db
            self.new_db = get_new_db()
            self.new_db.connect()
            print("✓ 新数据库系统连接成功")
            
            # 尝试导入旧系统（如果存在）
            try:
                from app.utils.db_manager import get_db_manager as get_old_db
                self.old_db = get_old_db()
                self.old_db.connect()
                print("✓ 旧数据库系统连接成功")
            except Exception as e:
                print(f"! 旧数据库系统不可用: {e}")
                self.old_db = None
                
            return True
        except Exception as e:
            print(f"✗ 数据库连接失败: {e}")
            return False
    
    def test_data_consistency(self):
        """测试数据一致性"""
        print("\n=== 数据一致性测试 ===")
        
        try:
            # 测试关注股票数据
            new_stocks = self.new_db.get_favorite_stocks()
            print(f"新系统关注股票数量: {len(new_stocks)}")
            
            if self.old_db:
                old_stocks = self.old_db.get_favorite_stocks()
                print(f"旧系统关注股票数量: {len(old_stocks)}")
                
                # 比较数据
                if len(new_stocks) == len(old_stocks):
                    print("✓ 关注股票数量一致")
                else:
                    print("⚠️  关注股票数量不一致")
            
            # 测试收藏研报数据
            new_reports = self.new_db.get_favorite_reports()
            print(f"新系统收藏研报数量: {len(new_reports)}")
            
            if self.old_db:
                old_reports = self.old_db.get_favorite_reports()
                print(f"旧系统收藏研报数量: {len(old_reports)}")
                
                if len(new_reports) == len(old_reports):
                    print("✓ 收藏研报数量一致")
                else:
                    print("⚠️  收藏研报数量不一致")
            
            # 测试研报数据
            new_report_count = self.new_db.get_reports_count()
            print(f"新系统研报总数: {new_report_count}")
            
            if hasattr(self.old_db, 'get_reports_count'):
                old_report_count = self.old_db.get_reports_count()
                print(f"旧系统研报总数: {old_report_count}")
                
                if new_report_count == old_report_count:
                    print("✓ 研报数量一致")
                else:
                    print("⚠️  研报数量不一致")
            
            return True
            
        except Exception as e:
            print(f"✗ 数据一致性测试失败: {e}")
            return False
    
    def test_crud_operations(self):
        """测试CRUD操作"""
        print("\n=== CRUD操作测试 ===")
        
        test_stock_code = f"TEST_{int(time.time())}"
        test_report_id = f"TEST_REPORT_{int(time.time())}"
        
        try:
            # 测试关注股票CRUD
            print("--- 关注股票CRUD测试 ---")
            
            # Create
            success = self.new_db.add_favorite_stock(test_stock_code, "测试股票", "CRUD测试")
            print(f"添加关注股票: {'✓' if success else '✗'}")
            
            # Read
            exists = self.new_db.is_stock_favorited(test_stock_code)
            print(f"查询关注股票: {'✓' if exists else '✗'}")
            
            stocks = self.new_db.get_favorite_stocks()
            found = any(stock['code'] == test_stock_code for stock in stocks)
            print(f"列表中找到股票: {'✓' if found else '✗'}")
            
            # Update (通过重新添加)
            success = self.new_db.add_favorite_stock(test_stock_code, "更新测试股票", "更新备注")
            print(f"更新关注股票: {'✓' if success else '✗'}")
            
            # Delete
            success = self.new_db.remove_favorite_stock(test_stock_code)
            print(f"删除关注股票: {'✓' if success else '✗'}")
            
            # 验证删除
            exists = self.new_db.is_stock_favorited(test_stock_code)
            print(f"验证删除结果: {'✓' if not exists else '✗'}")
            
            # 测试收藏研报CRUD
            print("\n--- 收藏研报CRUD测试 ---")
            
            # Create
            success = self.new_db.add_favorite_report(test_report_id, "测试研报", "CRUD测试")
            print(f"添加收藏研报: {'✓' if success else '✗'}")
            
            # Read
            exists = self.new_db.is_report_favorited(test_report_id)
            print(f"查询收藏研报: {'✓' if exists else '✗'}")
            
            reports = self.new_db.get_favorite_reports()
            found = any(report['report_id'] == test_report_id for report in reports)
            print(f"列表中找到研报: {'✓' if found else '✗'}")
            
            # Update
            success = self.new_db.add_favorite_report(test_report_id, "更新测试研报", "更新备注")
            print(f"更新收藏研报: {'✓' if success else '✗'}")
            
            # Delete
            success = self.new_db.remove_favorite_report(test_report_id)
            print(f"删除收藏研报: {'✓' if success else '✗'}")
            
            # 验证删除
            exists = self.new_db.is_report_favorited(test_report_id)
            print(f"验证删除结果: {'✓' if not exists else '✗'}")
            
            return True
            
        except Exception as e:
            print(f"✗ CRUD操作测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_report_operations(self):
        """测试研报操作"""
        print("\n=== 研报操作测试 ===")
        
        test_report_id = f"TEST_FULL_REPORT_{int(time.time())}"
        
        try:
            # 创建测试研报数据
            report_data = {
                'id': test_report_id,
                'title': '完整功能测试研报',
                'content': '这是一个用于测试完整功能的研报内容，包含各种测试数据。',
                'label': '测试标签',
                'create_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'code_list': ['000001', '000002', '000003'],
                'source': 'test',
                'art_url': 'http://test.com/report'
            }
            
            # 插入研报
            success = self.new_db.insert_report(report_data)
            print(f"插入研报: {'✓' if success else '✗'}")
            
            if success:
                # 查询研报详情
                report = self.new_db.get_report_by_id(test_report_id)
                print(f"查询研报详情: {'✓' if report else '✗'}")
                
                if report:
                    print(f"  - 标题: {report.get('title', 'N/A')}")
                    print(f"  - 标签: {report.get('label', 'N/A')}")
                    print(f"  - 股票代码: {report.get('code_list', [])}")
                
                # 更新研报评分
                success = self.new_db.update_report_rating(test_report_id, 8)
                print(f"更新研报评分: {'✓' if success else '✗'}")
                
                # 验证评分更新
                updated_report = self.new_db.get_report_by_id(test_report_id)
                if updated_report and updated_report.get('rating') == 8:
                    print("✓ 评分更新验证成功")
                else:
                    print("✗ 评分更新验证失败")
                
                # 测试搜索功能
                search_results = self.new_db.search_reports("完整功能测试")
                found = any(r.get('id') == test_report_id for r in search_results)
                print(f"搜索研报功能: {'✓' if found else '✗'}")
                
                # 测试分页查询
                reports = self.new_db.get_reports(page=1, page_size=5)
                print(f"分页查询研报: ✓ (获取到{len(reports)}个)")
                
                # 测试筛选查询
                filtered_reports = self.new_db.get_reports(page=1, page_size=10, label='测试标签')
                found = any(r.get('id') == test_report_id for r in filtered_reports)
                print(f"筛选查询研报: {'✓' if found else '✗'}")
                
                # 标记删除
                success = self.new_db.mark_report_deleted(test_report_id, "测试删除")
                print(f"标记研报删除: {'✓' if success else '✗'}")
                
                # 验证删除标记
                deleted_report = self.new_db.get_report_by_id(test_report_id, include_deleted=True)
                if deleted_report and deleted_report.get('is_deleted'):
                    print("✓ 删除标记验证成功")
                else:
                    print("✗ 删除标记验证失败")
                
                # 恢复研报
                success = self.new_db.restore_report(test_report_id)
                print(f"恢复研报: {'✓' if success else '✗'}")
                
                # 最终清理
                success = self.new_db.mark_report_deleted(test_report_id, "测试完成，清理数据")
                print(f"清理测试数据: {'✓' if success else '✗'}")
            
            return True
            
        except Exception as e:
            print(f"✗ 研报操作测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_statistics_and_queries(self):
        """测试统计和查询功能"""
        print("\n=== 统计和查询功能测试 ===")
        
        try:
            # 获取统计信息
            stats = self.new_db.get_report_statistics()
            print(f"获取研报统计: ✓")
            print(f"  - 总研报数: {stats.get('total_count', 0)}")
            print(f"  - 已删除数: {stats.get('deleted_count', 0)}")
            print(f"  - 标签分组: {len(stats.get('by_label', []))}")
            print(f"  - 评分分组: {len(stats.get('by_rating', []))}")
            
            # 测试各种查询条件
            print("\n--- 查询条件测试 ---")
            
            # 按评分查询
            high_rated = self.new_db.get_reports(page=1, page_size=5, rating_min=7)
            print(f"高评分研报查询: ✓ (找到{len(high_rated)}个)")
            
            # 按日期查询
            today = datetime.now().strftime('%Y-%m-%d')
            today_reports = self.new_db.get_reports(page=1, page_size=5, date=today)
            print(f"今日研报查询: ✓ (找到{len(today_reports)}个)")
            
            # 搜索功能
            search_results = self.new_db.search_reports("股票", limit=10)
            print(f"关键词搜索: ✓ (找到{len(search_results)}个)")
            
            # 获取总数
            total_count = self.new_db.get_reports_count()
            print(f"获取研报总数: ✓ ({total_count}个)")
            
            # 包含已删除的总数
            total_with_deleted = self.new_db.get_reports_count(include_deleted=True)
            print(f"包含已删除总数: ✓ ({total_with_deleted}个)")
            
            return True
            
        except Exception as e:
            print(f"✗ 统计和查询功能测试失败: {e}")
            return False
    
    def test_performance(self):
        """测试性能"""
        print("\n=== 性能测试 ===")
        
        try:
            # 批量查询性能
            start_time = time.time()
            for _ in range(10):
                self.new_db.get_favorite_stocks()
            query_time = time.time() - start_time
            print(f"批量查询关注股票(10次): {query_time:.3f}秒")
            
            # 搜索性能
            start_time = time.time()
            for _ in range(5):
                self.new_db.search_reports("测试", limit=20)
            search_time = time.time() - start_time
            print(f"批量搜索研报(5次): {search_time:.3f}秒")
            
            # 统计查询性能
            start_time = time.time()
            for _ in range(3):
                self.new_db.get_report_statistics()
            stats_time = time.time() - start_time
            print(f"批量统计查询(3次): {stats_time:.3f}秒")
            
            print("✓ 性能测试完成")
            return True
            
        except Exception as e:
            print(f"✗ 性能测试失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.new_db:
                self.new_db.close()
            if self.old_db:
                self.old_db.close()
            print("✓ 数据库连接已关闭")
        except Exception as e:
            print(f"⚠️  清理资源时出错: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("数据库迁移升级验证测试")
        print("=" * 60)
        
        # 设置数据库
        if not self.setup_databases():
            return False
        
        tests = [
            ("数据一致性测试", self.test_data_consistency),
            ("CRUD操作测试", self.test_crud_operations),
            ("研报操作测试", self.test_report_operations),
            ("统计查询测试", self.test_statistics_and_queries),
            ("性能测试", self.test_performance),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"✗ {test_name}执行异常: {e}")
                results.append((test_name, False))
        
        # 清理资源
        self.cleanup()
        
        # 输出结果
        print("\n" + "=" * 60)
        print("测试结果汇总:")
        print("=" * 60)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{total} 个测试通过")
        
        if passed == total:
            print("\n🎉 所有测试都通过了！")
            print("数据库迁移升级成功，新系统工作正常。")
            print("\n建议:")
            print("1. 可以安全地使用新的数据库系统")
            print("2. 在生产环境中逐步替换旧的导入语句")
            print("3. 监控系统运行状况")
        else:
            print("\n⚠️  部分测试失败，请检查问题后再使用新系统。")
        
        return passed == total


def main():
    """主函数"""
    validator = DatabaseMigrationValidator()
    success = validator.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
