# AI问股模块配置示例
# 复制此文件为 .env 并填入自己的配置

# API服务配置
API_HOST=localhost
API_PORT=8001

# 数据目录配置
REPORT_DATA_DIR=/Users/<USER>/PycharmProjects/own/data/kpl_report

# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# Anthropic Claude配置
CLAUDE_API_KEY=your_claude_api_key_here
CLAUDE_BASE_URL=https://api.anthropic.com
CLAUDE_MODEL=claude-3-sonnet-20240229

# Google Gemini配置
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_BASE_URL=
GEMINI_MODEL=gemini-2.0-flash

# Dify配置
DIFY_API_KEY=your_dify_api_key_here
DIFY_BASE_URL=https://api.dify.ai/v1

# 代理配置（可选）
HTTP_PROXY=
HTTPS_PROXY=

# 使用示例：
# 1. 复制此文件为 .env
# 2. 填入你的API密钥
# 3. 如果使用代理服务，可以自定义BASE_URL
# 4. 如果需要代理访问，填入代理地址 