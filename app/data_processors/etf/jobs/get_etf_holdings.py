import akshare as ak
import pandas as pd
import os
from tqdm import tqdm
from datetime import datetime

def get_etf_holdings():
    """
    获取所有ETF基金的成分股
    """
    input_path = "app/data/etf_data/etf_list.csv"
    output_dir = "app/data/etf_data/holdings"

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    if not os.path.exists(input_path):
        print(f"错误: 文件 {input_path} 不存在。请先运行 filter_etfs.py")
        return

    etf_list_df = pd.read_csv(input_path)
    # 提取基金代码列，并确保是字符串类型
    symbols = etf_list_df['基金代码'].astype(str)

    print(f"共找到 {len(symbols)} 只ETF基金，开始获取成分股...")

    # 获取当前年份
    current_year = str(datetime.now().year)

    holdings_df=None
    for symbol in tqdm(symbols, desc="获取ETF成分股"):
        try:
            # 获取最新的持仓，通常是前一个季度，年份可以给当年
            holdings_df = ak.fund_portfolio_hold_em(symbol=symbol, date=current_year)

            if holdings_df.empty:
                print(f"基金代码 {symbol} 在 {current_year} 年没有找到持仓数据。")
                continue
            # print(holdings_df.keys())
            # 获取报告季度用于文件名
            quarter = holdings_df['季度'].iloc[0]
            # 清理文件名中的非法字符
            quarter_filename = quarter.replace("年", "_").replace("季度股票投资明细", "")
            output_filename = f"{symbol}_{quarter_filename}.csv"
            output_path = os.path.join(output_dir, output_filename)

            # 如果文件已存在，则跳过
            if os.path.exists(output_path):
                # print(f"文件 {output_filename} 已存在，跳过。")
                continue

            holdings_df.to_csv(output_path, index=False)
            # print(f"已保存 {symbol} 的成分股至 {output_path}")

        except Exception as e:
            print(f"获取基金代码 {symbol} 的成分股时发生错误: {e}  ，keys{holdings_df.keys()}")

            continue
    
    print("所有ETF基金的成分股获取完成。")

if __name__ == "__main__":
    get_etf_holdings() 