"""
量化分析系统 - 定时任务调度器

实现每日自动更新和其他定时任务
"""
import sys
import time
import logging
import schedule
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.strategy.scripts.tasks import DailyUpdateTask, SystemMaintenanceTask

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data/strategy/logs/scheduler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        self.running = False
        self.last_daily_update = None
        self.last_maintenance = None
    
    def setup_schedules(self):
        """设置定时任务"""
        # 每日更新任务 - 工作日18:30执行
        schedule.every().monday.at("18:30").do(self.run_daily_update)
        schedule.every().tuesday.at("18:30").do(self.run_daily_update)
        schedule.every().wednesday.at("18:30").do(self.run_daily_update)
        schedule.every().thursday.at("18:30").do(self.run_daily_update)
        schedule.every().friday.at("18:30").do(self.run_daily_update)
        
        # 系统维护任务 - 每周日凌晨2:00执行
        schedule.every().sunday.at("02:00").do(self.run_maintenance)
        
        # 健康检查 - 每小时执行
        schedule.every().hour.do(self.run_health_check)
        
        logger.info("定时任务调度已设置")
        logger.info("- 每日更新: 工作日 18:30")
        logger.info("- 系统维护: 周日 02:00")
        logger.info("- 健康检查: 每小时")
    
    def run_daily_update(self):
        """执行每日更新任务"""
        logger.info("开始执行每日更新任务")
        
        try:
            task = DailyUpdateTask()
            args = type('Args', (), {
                'date': None,  # 使用当前日期
                'skip_data': False,
                'skip_indicators': False,
                'skip_patterns': False
            })()
            
            success = task._execute(args)
            
            if success:
                self.last_daily_update = datetime.now()
                logger.info("每日更新任务执行成功")
            else:
                logger.error("每日更新任务执行失败")
                
        except Exception as e:
            logger.error(f"每日更新任务异常: {e}")
            import traceback
            traceback.print_exc()
    
    def run_maintenance(self):
        """执行系统维护任务"""
        logger.info("开始执行系统维护任务")
        
        try:
            task = SystemMaintenanceTask()
            args = type('Args', (), {
                'cleanup': True,
                'optimize': True,
                'backup': False,
                'check_health': True
            })()
            
            success = task._execute(args)
            
            if success:
                self.last_maintenance = datetime.now()
                logger.info("系统维护任务执行成功")
            else:
                logger.error("系统维护任务执行失败")
                
        except Exception as e:
            logger.error(f"系统维护任务异常: {e}")
            import traceback
            traceback.print_exc()
    
    def run_health_check(self):
        """执行健康检查"""
        try:
            from ..data.manager import DataManager
            
            manager = DataManager()
            symbols = manager.get_available_symbols()
            
            if len(symbols) == 0:
                logger.warning("健康检查: 没有可用的股票数据")
            else:
                logger.info(f"健康检查: 系统正常，共有 {len(symbols)} 只股票数据")
                
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
    
    def start(self):
        """启动调度器"""
        self.running = True
        logger.info("任务调度器启动")
        
        # 设置定时任务
        self.setup_schedules()
        
        # 主循环
        try:
            while self.running:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在停止调度器...")
            self.stop()
    
    def stop(self):
        """停止调度器"""
        self.running = False
        schedule.clear()
        logger.info("任务调度器已停止")
    
    def get_status(self):
        """获取调度器状态"""
        return {
            'running': self.running,
            'last_daily_update': self.last_daily_update,
            'last_maintenance': self.last_maintenance,
            'next_jobs': [str(job) for job in schedule.jobs]
        }

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='量化分析系统定时任务调度器')
    parser.add_argument('--daemon', action='store_true', help='以守护进程模式运行')
    parser.add_argument('--test', action='store_true', help='测试模式，立即执行一次每日更新')
    
    args = parser.parse_args()
    
    scheduler = TaskScheduler()
    
    if args.test:
        # 测试模式
        logger.info("测试模式：立即执行每日更新任务")
        scheduler.run_daily_update()
        return 0
    
    if args.daemon:
        # 守护进程模式
        logger.info("以守护进程模式启动")
        # 这里可以添加守护进程的实现
    
    try:
        scheduler.start()
        return 0
    except Exception as e:
        logger.error(f"调度器启动失败: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
