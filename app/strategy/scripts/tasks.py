"""
量化分析系统任务模块

定义各种任务的具体实现
"""
import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

from ..core.batch_processor import get_batch_processor
from ..data.collector import DataCollector
from ..data.manager import DataManager
from ..patterns.engine import PatternEngine

logger = logging.getLogger(__name__)

class BaseTask:
    """基础任务类"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def run(self, args) -> int:
        """运行任务"""
        self.start_time = time.time()
        logger.info(f"开始执行任务: {self.__class__.__name__}")
        
        try:
            result = self._execute(args)
            self.end_time = time.time()
            
            duration = self.end_time - self.start_time
            logger.info(f"任务完成: {self.__class__.__name__}, 耗时: {duration:.2f}s")
            
            return 0 if result else 1
            
        except Exception as e:
            self.end_time = time.time()
            logger.error(f"任务失败: {self.__class__.__name__}, 错误: {e}")
            raise
    
    def _execute(self, args) -> bool:
        """子类需要实现的执行方法"""
        raise NotImplementedError

class DataCollectionTask(BaseTask):
    """数据采集任务"""

    def __init__(self, use_miniqmt: bool = True):
        super().__init__()
        # 优先使用 MiniQMT 采集器，因为它能正确保存到按日期格式的文件
        if use_miniqmt:
            try:
                # 动态导入 MiniQMT 采集器，避免模块级别的导入错误
                from ..data.miniqmt_collector import MiniQMTCollector
                self.collector = MiniQMTCollector()
                logger.info("使用 MiniQMT 数据采集器")
            except ImportError as e:
                logger.warning(f"MiniQMT 模块不可用，回退到 akshare: {e}")
                self.collector = DataCollector()
                logger.info("使用 akshare 数据采集器")
            except Exception as e:
                logger.warning(f"MiniQMT 采集器初始化失败，回退到 akshare: {e}")
                self.collector = DataCollector()
                logger.info("使用 akshare 数据采集器")
        else:
            self.collector = DataCollector()
            logger.info("使用 akshare 数据采集器")
        self.manager = DataManager()
    
    def _execute(self, args) -> bool:
        """执行数据采集"""
        # 确定要采集的股票
        if args.symbols:
            symbols = [s.strip() for s in args.symbols.split(',')]
        elif args.all:
            # 获取所有A股股票
            stock_list = self.collector.get_stock_list()
            symbols = stock_list['code'].tolist()
        else:
            # 默认采集样本股票
            symbols = ['000001', '000002', '000858', '002415', '300502', '600000', '600036', '600519']

        logger.info(f"准备采集 {len(symbols)} 只股票数据")

        # 计算日期范围
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=getattr(args, 'days', 5))).strftime("%Y-%m-%d")

        # 如果使用 MiniQMT 采集器，优先使用批量采集
        # 动态检查采集器类型
        if hasattr(self.collector, 'collect_all_stocks_data') and hasattr(self.collector, 'update_recent_data'):
            try:
                logger.info(f"使用 MiniQMT 批量采集数据: {start_date} 到 {end_date}")

                # 使用批量采集方法
                if hasattr(args, 'all') and args.all:
                    # 采集所有股票
                    results = self.collector.collect_all_stocks_data(
                        start_date=start_date,
                        end_date=end_date,
                        batch_size=100,  # 批量大小
                        validate_data=False  # 关闭验证提高速度
                    )
                else:
                    # 采集指定股票
                    results = {}
                    for symbol in symbols:
                        success = self.collector.collect_stock_data(symbol, start_date, end_date)
                        results[symbol] = success

                success_count = sum(results.values())
                total_count = len(results)

                logger.info(f"MiniQMT 数据采集完成: {success_count}/{total_count} 成功")
                return success_count > 0

            except Exception as e:
                logger.error(f"MiniQMT 批量采集失败: {e}")
                # 回退到逐个采集
                logger.info("回退到逐个采集模式...")

        # 逐个采集（akshare 或 MiniQMT 回退模式）
        success_count = 0
        total_count = len(symbols)

        for i, symbol in enumerate(symbols, 1):
            logger.info(f"[{i}/{total_count}] 采集 {symbol}...")

            try:
                success = self.collector.collect_stock_data(symbol, start_date, end_date)

                if success:
                    success_count += 1
                    logger.info(f"✓ {symbol} 采集成功")
                else:
                    logger.warning(f"❌ {symbol} 采集失败")

            except Exception as e:
                logger.error(f"❌ {symbol} 采集异常: {e}")

        logger.info(f"数据采集完成: {success_count}/{total_count} 成功")
        return success_count > 0

class IndicatorCalculationTask(BaseTask):
    """指标计算任务"""
    
    def __init__(self):
        super().__init__()
        self.manager = DataManager()
    
    def _execute(self, args) -> bool:
        """执行指标计算"""
        # 确定要计算的股票
        if args.symbols:
            symbols = [s.strip() for s in args.symbols.split(',')]
        elif args.all:
            symbols = self.manager.get_available_symbols()
        else:
            # 默认计算所有可用股票
            symbols = self.manager.get_available_symbols()
        
        if not symbols:
            logger.warning("没有可用的股票数据")
            return False
        
        logger.info(f"准备计算 {len(symbols)} 只股票指标")
        
        # 使用批量处理器
        processor = get_batch_processor()
        processor.max_workers = args.workers

        result = processor.process_symbols_batch(
            symbols=symbols,
            batch_size=args.batch_size,
            force_recalculate=args.force
        )
        
        success_rate = result['total_processed'] / result['total_symbols'] if result['total_symbols'] > 0 else 0
        logger.info(f"指标计算完成: 成功率 {success_rate:.2%}")
        
        return success_rate > 0.5  # 成功率超过50%认为任务成功

class PatternAnalysisTask(BaseTask):
    """形态分析任务"""
    
    def __init__(self):
        super().__init__()
        self.manager = DataManager()
        self.pattern_engine = PatternEngine()
    
    def _execute(self, args) -> bool:
        """执行形态分析"""
        # 确定要分析的股票
        if args.symbols:
            symbols = [s.strip() for s in args.symbols.split(',')]
        elif args.all:
            symbols = self.manager.get_available_symbols()
        else:
            # 默认分析所有可用股票
            symbols = self.manager.get_available_symbols()
        
        if not symbols:
            logger.warning("没有可用的股票数据")
            return False
        
        logger.info(f"准备分析 {len(symbols)} 只股票形态")
        
        # 执行形态分析
        results = []
        processed_count = 0
        
        for i, symbol in enumerate(symbols, 1):
            logger.info(f"[{i}/{len(symbols)}] 分析 {symbol}...")
            
            try:
                # 加载数据
                data = self.manager.load_stock_data(symbol)
                
                if data.empty:
                    continue
                
                # 检测形态
                patterns = self.pattern_engine.detect_all_patterns(data)
                
                # 筛选符合条件的形态
                for pattern_name, detections in patterns.items():
                    for detection in detections:
                        # 应用筛选条件
                        if args.signal_type and detection['signal'] != args.signal_type:
                            continue
                        
                        if detection['confidence'] < args.min_confidence:
                            continue
                        
                        results.append({
                            'symbol': symbol,
                            'pattern_name': pattern_name,
                            'signal': detection['signal'],
                            'confidence': detection['confidence'],
                            'date': str(detection['date']),
                            'description': detection['description']
                        })
                
                processed_count += 1
                
            except Exception as e:
                logger.error(f"❌ {symbol} 形态分析异常: {e}")
        
        logger.info(f"形态分析完成: 处理 {processed_count} 只股票，发现 {len(results)} 个形态")
        
        # 保存结果
        if args.output and results:
            import pandas as pd
            df = pd.DataFrame(results)
            df.to_csv(args.output, index=False)
            logger.info(f"结果已保存到: {args.output}")
        
        return processed_count > 0

class DailyUpdateTask(BaseTask):
    """每日更新任务"""
    
    def _execute(self, args) -> bool:
        """执行每日更新"""
        target_date = args.date if args.date else datetime.now().strftime("%Y-%m-%d")
        logger.info(f"执行每日更新任务: {target_date}")
        
        success = True
        
        # 1. 数据采集
        if not args.skip_data:
            logger.info("1. 执行数据采集...")
            # 优先使用 MiniQMT 采集器
            data_task = DataCollectionTask(use_miniqmt=True)
            data_args = type('Args', (), {
                'symbols': None,
                'all': True,
                'days': 5,  # 更新最近5天
                'workers': 4,
                'force': False
            })()

            if not data_task._execute(data_args):
                logger.warning("数据采集失败")
                success = False
        
        # 2. 指标计算
        if not args.skip_indicators:
            logger.info("2. 执行指标计算...")
            indicator_task = IndicatorCalculationTask()
            indicator_args = type('Args', (), {
                'symbols': None,
                'all': True,
                'batch_size': 50,
                'workers': 4,
                'groups': None,
                'force': False
            })()
            
            if not indicator_task._execute(indicator_args):
                logger.warning("指标计算失败")
                success = False
        
        # 3. 形态分析
        if not args.skip_patterns:
            logger.info("3. 执行形态分析...")
            pattern_task = PatternAnalysisTask()
            pattern_args = type('Args', (), {
                'symbols': None,
                'all': True,
                'signal_type': None,
                'min_confidence': 0.6,
                'output': f"data/strategy/reports/patterns_{target_date}.csv"
            })()
            
            if not pattern_task._execute(pattern_args):
                logger.warning("形态分析失败")
                success = False
        
        logger.info(f"每日更新任务完成: {'成功' if success else '部分失败'}")
        return success

class SystemMaintenanceTask(BaseTask):
    """系统维护任务"""
    
    def _execute(self, args) -> bool:
        """执行系统维护"""
        logger.info("执行系统维护任务")
        
        success = True
        
        # 清理临时文件
        if args.cleanup:
            logger.info("清理临时文件...")
            try:
                temp_dir = Path("data/strategy/temp")
                if temp_dir.exists():
                    import shutil
                    shutil.rmtree(temp_dir)
                    temp_dir.mkdir(parents=True)
                logger.info("✓ 临时文件清理完成")
            except Exception as e:
                logger.error(f"❌ 临时文件清理失败: {e}")
                success = False
        
        # 优化数据库
        if args.optimize:
            logger.info("优化数据库...")
            try:
                manager = DataManager()
                import duckdb
                with duckdb.connect(manager.db_path) as conn:
                    conn.execute("VACUUM")
                    conn.execute("ANALYZE")
                logger.info("✓ 数据库优化完成")
            except Exception as e:
                logger.error(f"❌ 数据库优化失败: {e}")
                success = False
        
        # 健康检查
        if args.check_health:
            logger.info("执行健康检查...")
            try:
                manager = DataManager()
                symbols = manager.get_available_symbols()
                logger.info(f"✓ 数据库连接正常，共有 {len(symbols)} 只股票数据")
            except Exception as e:
                logger.error(f"❌ 健康检查失败: {e}")
                success = False
        
        logger.info(f"系统维护任务完成: {'成功' if success else '部分失败'}")
        return success
