"""
量化分析系统 - 统一脚本管理器

提供所有量化分析任务的统一执行入口
"""
import sys
import argparse
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.strategy.scripts.tasks import (
    DataCollectionTask,
    IndicatorCalculationTask,
    PatternAnalysisTask,
    DailyUpdateTask,
    SystemMaintenanceTask
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description='量化分析系统统一脚本管理器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 数据采集
  python app/strategy/scripts/main.py data-collection --symbols 000001,000002 --days 30
  
  # 指标计算
  python app/strategy/scripts/main.py indicators --batch-size 50 --workers 4
  
  # 形态分析
  python app/strategy/scripts/main.py patterns --signal-type BULLISH
  
  # 每日更新
  python app/strategy/scripts/main.py daily-update
  
  # 系统维护
  python app/strategy/scripts/main.py maintenance --cleanup --optimize
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 数据采集命令
    data_parser = subparsers.add_parser('data-collection', help='数据采集任务')
    data_parser.add_argument('--symbols', type=str, help='股票代码列表，逗号分隔')
    data_parser.add_argument('--all', action='store_true', help='采集所有股票')
    data_parser.add_argument('--days', type=int, default=30, help='采集天数')
    data_parser.add_argument('--workers', type=int, default=4, help='并发数')
    data_parser.add_argument('--force', action='store_true', help='强制重新采集')
    
    # 指标计算命令
    indicators_parser = subparsers.add_parser('indicators', help='指标计算任务')
    indicators_parser.add_argument('--symbols', type=str, help='股票代码列表，逗号分隔')
    indicators_parser.add_argument('--all', action='store_true', help='计算所有股票')
    indicators_parser.add_argument('--batch-size', type=int, default=50, help='批次大小')
    indicators_parser.add_argument('--workers', type=int, default=4, help='并发数')
    indicators_parser.add_argument('--groups', type=str, help='指标组，逗号分隔')
    indicators_parser.add_argument('--force', action='store_true', help='强制重新计算')
    
    # 形态分析命令
    patterns_parser = subparsers.add_parser('patterns', help='形态分析任务')
    patterns_parser.add_argument('--symbols', type=str, help='股票代码列表，逗号分隔')
    patterns_parser.add_argument('--all', action='store_true', help='分析所有股票')
    patterns_parser.add_argument('--signal-type', type=str, choices=['BULLISH', 'BEARISH', 'NEUTRAL'], 
                               help='信号类型筛选')
    patterns_parser.add_argument('--min-confidence', type=float, default=0.6, help='最小置信度')
    patterns_parser.add_argument('--output', type=str, help='输出文件路径')
    
    # 每日更新命令
    daily_parser = subparsers.add_parser('daily-update', help='每日更新任务')
    daily_parser.add_argument('--date', type=str, help='指定日期 YYYY-MM-DD')
    daily_parser.add_argument('--skip-data', action='store_true', help='跳过数据采集')
    daily_parser.add_argument('--skip-indicators', action='store_true', help='跳过指标计算')
    daily_parser.add_argument('--skip-patterns', action='store_true', help='跳过形态分析')
    
    # 系统维护命令
    maintenance_parser = subparsers.add_parser('maintenance', help='系统维护任务')
    maintenance_parser.add_argument('--cleanup', action='store_true', help='清理临时文件')
    maintenance_parser.add_argument('--optimize', action='store_true', help='优化数据库')
    maintenance_parser.add_argument('--backup', action='store_true', help='备份数据')
    maintenance_parser.add_argument('--check-health', action='store_true', help='健康检查')
    
    return parser

def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        # 根据命令执行相应任务
        if args.command == 'data-collection':
            task = DataCollectionTask()
            return task.run(args)
            
        elif args.command == 'indicators':
            task = IndicatorCalculationTask()
            return task.run(args)
            
        elif args.command == 'patterns':
            task = PatternAnalysisTask()
            return task.run(args)
            
        elif args.command == 'daily-update':
            task = DailyUpdateTask()
            return task.run(args)
            
        elif args.command == 'maintenance':
            task = SystemMaintenanceTask()
            return task.run(args)
            
        else:
            logger.error(f"未知命令: {args.command}")
            return 1
            
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        return 1
    except Exception as e:
        logger.error(f"执行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    exit(main())
