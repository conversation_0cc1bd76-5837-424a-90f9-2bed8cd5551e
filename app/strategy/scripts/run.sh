#!/bin/bash
# 量化分析系统便捷执行脚本

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

# 进入项目根目录
cd "$PROJECT_ROOT"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "量化分析系统便捷执行脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "可用命令:"
    echo "  demo                    运行快速演示"
    echo "  collect [symbols]       数据采集 (可选指定股票代码，逗号分隔)"
    echo "  indicators [symbols] [--force]  计算指标 (--force 强制重新计算)"
    echo "  patterns [symbols] [--signal TYPE]  形态分析 (--signal 指定信号类型)"
    echo "  daily                   每日更新"
    echo "  update-data             更新数据（仅数据采集，不执行指标计算）"
    echo "  maintenance             系统维护"
    echo "  scheduler               启动定时任务调度器"
    echo "  status                  查看系统状态"
    echo "  init                    初始化系统环境"
    echo "  logs                    查看最新日志"
    echo "  check-data              检查数据完整性"
    echo "  help                    显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 demo                 # 运行快速演示"
    echo "  $0 collect 000001,000002 # 采集指定股票数据"
    echo "  $0 indicators --force   # 强制重新计算所有指标"
    echo "  $0 indicators 000001,000002 # 计算指定股票指标"
    echo "  $0 patterns --signal BULLISH # 分析看涨形态"
    echo "  $0 patterns 000001 --signal BEARISH # 分析指定股票看跌形态"
    echo "  $0 daily                # 执行每日更新"
    echo "  $0 update-data          # 仅更新数据"
    echo "  $0 scheduler            # 启动定时任务"
    echo "  $0 init                 # 初始化系统环境"
    echo "  $0 logs                 # 查看最新日志"
    echo "  $0 check-data           # 检查数据完整性"
}

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装或不在PATH中"
        exit 1
    fi

    # 检查必要的包
    print_info "检查Python依赖包..."
    python3 -c "
import sys
missing_packages = []
try:
    import pandas
except ImportError:
    missing_packages.append('pandas')
try:
    import numpy
except ImportError:
    missing_packages.append('numpy')
try:
    import duckdb
except ImportError:
    missing_packages.append('duckdb')
try:
    import akshare
except ImportError:
    missing_packages.append('akshare')

if missing_packages:
    print(f'缺少以下包: {missing_packages}')
    sys.exit(1)
else:
    print('所有必要的包都已安装')
    sys.exit(0)
" 2>/dev/null

    if [ $? -ne 0 ]; then
        print_warning "缺少必要的Python包，请运行: pip install -r requirements.txt"
        return 1
    fi

    print_success "Python环境检查通过"
    return 0
}

# 运行快速演示
run_demo() {
    print_info "运行快速演示..."
    python3 app/strategy/examples/quick_demo.py
}

# 数据采集
run_collect() {
    local symbols="$1"
    print_info "开始数据采集..."

    # 检查是否有 MiniQMT 环境
    python3 -c "
try:
    from xtquant import xtdata
    print('✓ 检测到 MiniQMT 环境，将使用 MiniQMT 采集器')
    exit(0)
except ImportError:
    print('⚠️  未检测到 MiniQMT 环境，将使用 akshare 采集器')
    print('   注意：akshare 可能存在网络连接问题')
    exit(1)
" 2>/dev/null

    miniqmt_available=$?

    if [ -n "$symbols" ]; then
        python3 app/strategy/scripts/main.py data-collection --symbols "$symbols"
    else
        python3 app/strategy/scripts/main.py data-collection --all
    fi
}

# 指标计算
run_indicators() {
    local symbols=""
    local force_flag=""

    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                force_flag="--force"
                shift
                ;;
            *)
                if [ -z "$symbols" ]; then
                    symbols="$1"
                fi
                shift
                ;;
        esac
    done

    print_info "开始指标计算..."
    if [ -n "$symbols" ]; then
        python3 app/strategy/scripts/main.py indicators --symbols "$symbols" --batch-size 50 --workers 4 $force_flag
    else
        python3 app/strategy/scripts/main.py indicators --all --batch-size 50 --workers 4 $force_flag
    fi
}

# 形态分析
run_patterns() {
    local signal_flag=""
    local symbols=""

    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --signal)
                if [ -n "$2" ]; then
                    signal_flag="--signal-type $2"
                    shift 2
                else
                    print_error "参数 --signal 需要指定信号类型"
                    return 1
                fi
                ;;
            *)
                if [ -z "$symbols" ]; then
                    symbols="$1"
                fi
                shift
                ;;
        esac
    done

    print_info "开始形态分析..."
    if [ -n "$symbols" ]; then
        python3 app/strategy/scripts/main.py patterns --symbols "$symbols" $signal_flag
    else
        python3 app/strategy/scripts/main.py patterns --all $signal_flag
    fi
}

# 每日更新
run_daily() {
    print_info "开始每日更新..."
    print_info "执行策略：跳过数据采集，仅执行指标计算和形态分析"
    print_info "如需更新数据，请使用: $0 update-data"

    # 跳过数据采集，只执行指标计算和形态分析
    python3 app/strategy/scripts/main.py daily-update --skip-data
}

# 仅更新数据
run_update_data() {
    print_info "开始数据更新..."

    # 检查现有数据状态
    cd "$PROJECT_ROOT"
    python3 -c "
import sys
sys.path.insert(0, '.')
from pathlib import Path
import pandas as pd
from datetime import datetime, timedelta

print('=== 数据更新状态检查 ===')

# 检查最新数据
raw_path = Path('data/strategy/raw')
date_files = list(raw_path.glob('*/*/202*.parquet'))

if not date_files:
    print('❌ 没有找到现有数据文件')
    exit(1)

# 获取最新数据文件
latest_file = sorted(date_files)[-1]
latest_date_str = latest_file.stem
latest_date = f'{latest_date_str[:4]}-{latest_date_str[4:6]}-{latest_date_str[6:8]}'

print(f'最新数据文件: {latest_file.name}')
print(f'最新数据日期: {latest_date}')

# 检查是否需要更新
today = datetime.now().date()
latest_dt = datetime.strptime(latest_date, '%Y-%m-%d').date()
days_behind = (today - latest_dt).days

print(f'当前日期: {today}')
print(f'数据落后天数: {days_behind}')

if days_behind <= 1:
    print('✓ 数据已是最新，无需更新')
    exit(0)
elif days_behind > 10:
    print('⚠️  数据落后超过10天，建议手动检查数据源')
    exit(2)
else:
    print(f'需要更新最近 {days_behind} 天的数据')
    exit(3)
"

    update_status=$?

    case $update_status in
        0)
            print_success "数据已是最新"
            return 0
            ;;
        1)
            print_error "没有找到现有数据"
            return 1
            ;;
        2)
            print_warning "数据落后超过10天，请检查数据源"
            print_info "建议联系数据提供方或检查数据采集流程"
            return 1
            ;;
        3)
            print_info "检测到数据需要更新，但当前环境不支持自动数据采集"
            print_info "请使用以下方式更新数据："
            print_info "1. 如果有 MiniQMT 环境，请安装 xtquant 模块"
            print_info "2. 或者手动运行数据采集脚本"
            print_info "3. 或者联系数据提供方获取最新数据"
            return 1
            ;;
        *)
            print_error "数据状态检查失败"
            return 1
            ;;
    esac
}

# 系统维护
run_maintenance() {
    print_info "开始系统维护..."
    python3 app/strategy/scripts/main.py maintenance --cleanup --optimize --check-health
}

# 启动调度器
run_scheduler() {
    print_info "启动定时任务调度器..."
    python3 app/strategy/scripts/scheduler.py
}

# 查看系统状态
show_status() {
    print_info "查看系统状态..."

    # 检查数据目录
    if [ -d "data/strategy" ]; then
        print_success "数据目录存在"

        # 统计原始数据文件
        if [ -d "data/strategy/raw" ]; then
            print_success "原始数据目录存在"

            # 统计按股票代码存储的文件
            stock_files=$(find "data/strategy/raw" -name "*.parquet" ! -name "202*" 2>/dev/null | wc -l)
            print_info "股票数据文件数量: $stock_files"

            # 统计按日期存储的文件
            date_files=$(find "data/strategy/raw" -name "202*.parquet" 2>/dev/null | wc -l)
            print_info "日期数据文件数量: $date_files"

            # 检查最新数据
            latest_date_file=$(find "data/strategy/raw" -name "202*.parquet" 2>/dev/null | sort | tail -1)
            if [ -n "$latest_date_file" ]; then
                latest_date=$(basename "$latest_date_file" .parquet)
                formatted_date="${latest_date:0:4}-${latest_date:4:2}-${latest_date:6:2}"
                print_info "最新数据日期: $formatted_date"
            fi
        else
            print_warning "原始数据目录不存在"
        fi

        # 检查数据库
        if [ -f "data/strategy/strategy.duckdb" ]; then
            print_success "数据库文件存在"
            # 获取数据库文件大小
            db_size=$(du -h "data/strategy/strategy.duckdb" 2>/dev/null | cut -f1)
            print_info "数据库文件大小: $db_size"
        else
            print_warning "数据库文件不存在"
        fi
    else
        print_warning "数据目录不存在，将创建目录结构..."
        mkdir -p "data/strategy/raw"
        mkdir -p "data/strategy/logs"
        mkdir -p "data/strategy/cache"
        print_success "目录结构已创建"
    fi

    # 检查日志
    if [ -d "data/strategy/logs" ]; then
        print_success "日志目录存在"
        log_count=$(find "data/strategy/logs" -name "*.log" 2>/dev/null | wc -l)
        print_info "日志文件数量: $log_count"

        # 显示最新日志文件
        latest_log=$(find "data/strategy/logs" -name "*.log" -type f -exec ls -t {} + 2>/dev/null | head -1)
        if [ -n "$latest_log" ]; then
            print_info "最新日志文件: $(basename "$latest_log")"
        fi
    else
        print_warning "日志目录不存在"
    fi

    # 检查Python环境
    print_info "检查Python环境..."
    check_python

    # 运行Python状态检查
    print_info "检查数据管理器状态..."
    cd "$PROJECT_ROOT"
    python3 -c "
import sys
sys.path.insert(0, '.')
try:
    from app.strategy.data.manager import DataManager
    from pathlib import Path
    import pandas as pd

    manager = DataManager()
    symbols = manager.get_available_symbols()
    print(f'✓ 可用股票数量: {len(symbols)}')

    # 检查原始数据统计
    raw_path = Path('data/strategy/raw')
    if raw_path.exists():
        # 统计数据覆盖的时间范围
        date_files = list(raw_path.glob('*/*/202*.parquet'))
        if date_files:
            dates = [f.stem for f in date_files]
            dates.sort()
            start_date = f'{dates[0][:4]}-{dates[0][4:6]}-{dates[0][6:8]}'
            end_date = f'{dates[-1][:4]}-{dates[-1][4:6]}-{dates[-1][6:8]}'
            print(f'✓ 数据时间范围: {start_date} 到 {end_date}')
            print(f'✓ 数据覆盖天数: {len(dates)} 天')

            # 检查最新数据的股票数量
            latest_file = raw_path / dates[-1][:4] / dates[-1][4:6] / f'{dates[-1]}.parquet'
            if latest_file.exists():
                latest_data = pd.read_parquet(latest_file)
                print(f'✓ 最新数据包含股票: {len(latest_data)} 只')

    # 检查数据库最后更新时间
    db_path = Path('data/strategy/strategy.duckdb')
    if db_path.exists():
        import datetime
        mtime = datetime.datetime.fromtimestamp(db_path.stat().st_mtime)
        print(f'✓ 数据库最后更新: {mtime.strftime(\"%Y-%m-%d %H:%M:%S\")}')

except ImportError as e:
    print(f'❌ 导入模块失败: {e}')
    print('请确保已安装所有依赖包')
except Exception as e:
    print(f'❌ 状态检查失败: {e}')
" 2>/dev/null || print_warning "Python状态检查失败"
}

# 初始化系统环境
init_system() {
    print_info "初始化系统环境..."

    # 创建必要的目录结构
    print_info "创建目录结构..."
    mkdir -p "data/strategy/logs"
    mkdir -p "data/strategy/cache"
    mkdir -p "data/strategy/backups"
    print_success "目录结构创建完成"

    # 检查Python环境
    print_info "检查Python环境..."
    if ! check_python; then
        print_error "Python环境检查失败，请先安装必要的依赖包"
        return 1
    fi

    # 初始化数据管理器
    print_info "初始化数据管理器..."
    cd "$PROJECT_ROOT"
    python3 -c "
import sys
sys.path.insert(0, '.')
try:
    from app.strategy.data.manager import DataManager
    manager = DataManager()
    print('✓ 数据管理器初始化成功')
except Exception as e:
    print(f'❌ 数据管理器初始化失败: {e}')
    sys.exit(1)
" || return 1

    print_success "系统环境初始化完成"
}

# 查看最新日志
show_logs() {
    print_info "查看最新日志..."

    if [ ! -d "data/strategy/logs" ]; then
        print_warning "日志目录不存在"
        return 1
    fi

    # 查找最新的日志文件
    latest_log=$(find "data/strategy/logs" -name "*.log" -type f -exec ls -t {} + 2>/dev/null | head -1)

    if [ -z "$latest_log" ]; then
        print_warning "没有找到日志文件"
        return 1
    fi

    print_info "显示最新日志文件: $(basename "$latest_log")"
    echo "----------------------------------------"
    tail -50 "$latest_log"
    echo "----------------------------------------"
    print_info "显示最后50行，完整日志请查看: $latest_log"
}

# 检查数据完整性
check_data_integrity() {
    print_info "检查数据完整性..."

    if [ ! -d "data/strategy/raw" ]; then
        print_error "原始数据目录不存在"
        return 1
    fi

    cd "$PROJECT_ROOT"
    python3 -c "
import sys
sys.path.insert(0, '.')
from pathlib import Path
import pandas as pd
from datetime import datetime, timedelta
import numpy as np

def check_data_integrity():
    raw_path = Path('data/strategy/raw')

    print('=== 数据完整性检查报告 ===')
    print()

    # 1. 检查数据文件结构
    print('1. 数据文件结构检查:')

    # 统计按年月分布的文件
    year_months = {}
    stock_files = 0
    date_files = 0

    for year_dir in raw_path.iterdir():
        if year_dir.is_dir() and year_dir.name.isdigit():
            for month_dir in year_dir.iterdir():
                if month_dir.is_dir() and month_dir.name.isdigit():
                    key = f'{year_dir.name}-{month_dir.name}'
                    files = list(month_dir.glob('*.parquet'))

                    stock_count = len([f for f in files if not f.name.startswith('202')])
                    date_count = len([f for f in files if f.name.startswith('202')])

                    year_months[key] = {'stock_files': stock_count, 'date_files': date_count}
                    stock_files += stock_count
                    date_files += date_count

    print(f'   总股票文件数: {stock_files}')
    print(f'   总日期文件数: {date_files}')
    print(f'   覆盖年月数: {len(year_months)}')
    print()

    # 2. 检查日期连续性
    print('2. 日期连续性检查:')

    date_files_list = []
    for year_dir in raw_path.iterdir():
        if year_dir.is_dir() and year_dir.name.isdigit():
            for month_dir in year_dir.iterdir():
                if month_dir.is_dir() and month_dir.name.isdigit():
                    for file in month_dir.glob('202*.parquet'):
                        date_files_list.append(file.stem)

    if date_files_list:
        date_files_list.sort()
        print(f'   最早数据: {date_files_list[0][:4]}-{date_files_list[0][4:6]}-{date_files_list[0][6:8]}')
        print(f'   最新数据: {date_files_list[-1][:4]}-{date_files_list[-1][4:6]}-{date_files_list[-1][6:8]}')
        print(f'   数据天数: {len(date_files_list)}')

        # 检查缺失的交易日
        start_date = datetime.strptime(date_files_list[0], '%Y%m%d')
        end_date = datetime.strptime(date_files_list[-1], '%Y%m%d')

        missing_dates = []
        current_date = start_date
        while current_date <= end_date:
            # 跳过周末
            if current_date.weekday() < 5:  # 0-4 是周一到周五
                date_str = current_date.strftime('%Y%m%d')
                if date_str not in date_files_list:
                    missing_dates.append(date_str)
            current_date += timedelta(days=1)

        if missing_dates:
            print(f'   ⚠️  缺失交易日数量: {len(missing_dates)}')
            if len(missing_dates) <= 10:
                for date in missing_dates:
                    formatted = f'{date[:4]}-{date[4:6]}-{date[6:8]}'
                    print(f'      缺失: {formatted}')
            else:
                print(f'      最近缺失: {missing_dates[-5:]}')
        else:
            print('   ✓ 交易日数据完整')
    print()

    # 3. 检查最新数据质量
    print('3. 最新数据质量检查:')

    if date_files_list:
        latest_file = None
        for year_dir in raw_path.iterdir():
            if year_dir.is_dir():
                for month_dir in year_dir.iterdir():
                    if month_dir.is_dir():
                        file_path = month_dir / f'{date_files_list[-1]}.parquet'
                        if file_path.exists():
                            latest_file = file_path
                            break

        if latest_file:
            try:
                df = pd.read_parquet(latest_file)
                print(f'   最新数据文件: {latest_file.name}')
                print(f'   股票数量: {len(df)}')
                print(f'   数据列数: {len(df.columns)}')

                # 检查关键字段
                required_cols = ['symbol', 'date', 'open', 'high', 'low', 'close', 'volume']
                missing_cols = [col for col in required_cols if col not in df.columns]
                if missing_cols:
                    print(f'   ❌ 缺失关键列: {missing_cols}')
                else:
                    print('   ✓ 关键列完整')

                # 检查数据质量
                null_counts = df[required_cols].isnull().sum()
                if null_counts.sum() > 0:
                    print(f'   ⚠️  空值统计: {null_counts[null_counts > 0].to_dict()}')
                else:
                    print('   ✓ 无空值')

                # 检查价格数据合理性
                price_cols = ['open', 'high', 'low', 'close']
                for col in price_cols:
                    if col in df.columns:
                        invalid_prices = (df[col] <= 0).sum()
                        if invalid_prices > 0:
                            print(f'   ❌ {col}列有{invalid_prices}个无效价格(<=0)')

                print('   ✓ 价格数据基本合理')

            except Exception as e:
                print(f'   ❌ 读取最新数据失败: {e}')

    print()
    print('=== 检查完成 ===')

check_data_integrity()
" || print_error "数据完整性检查失败"
}

# 错误处理函数
handle_error() {
    local exit_code=$?
    local line_number=$1
    print_error "脚本在第 $line_number 行发生错误，退出码: $exit_code"
    exit $exit_code
}

# 设置错误处理
trap 'handle_error $LINENO' ERR

# 主逻辑
main() {
    # 记录开始时间
    local start_time=$(date '+%Y-%m-%d %H:%M:%S')
    print_info "脚本开始执行: $start_time"

    # 确保在项目根目录
    cd "$PROJECT_ROOT"

    # 如果没有参数，显示帮助
    if [ $# -eq 0 ]; then
        show_help
        return 0
    fi

    # 检查Python环境（除了help和init命令）
    if [ "$1" != "help" ] && [ "$1" != "--help" ] && [ "$1" != "-h" ] && [ "$1" != "init" ]; then
        if ! check_python; then
            print_error "Python环境检查失败，请运行 '$0 init' 初始化环境"
            return 1
        fi
    fi

    # 解析命令
    case "$1" in
        "demo")
            run_demo
            ;;
        "collect")
            run_collect "$2"
            ;;
        "indicators")
            shift
            run_indicators "$@"
            ;;
        "patterns")
            shift
            run_patterns "$@"
            ;;
        "daily")
            run_daily
            ;;
        "update-data")
            run_update_data
            ;;
        "maintenance")
            run_maintenance
            ;;
        "scheduler")
            run_scheduler
            ;;
        "status")
            show_status
            ;;
        "init")
            init_system
            ;;
        "logs")
            show_logs
            ;;
        "check-data")
            check_data_integrity
            ;;
        "help"|"--help"|"-h"|"")
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac

    # 记录结束时间
    local end_time=$(date '+%Y-%m-%d %H:%M:%S')
    print_success "脚本执行完成: $end_time"
}

# 执行主函数
main "$@"
