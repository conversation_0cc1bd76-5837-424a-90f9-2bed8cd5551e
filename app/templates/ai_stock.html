{% extends "base.html" %}

{% block title %}AI问股 - 研报展示平台{% endblock %}

{% block head %}
<style>
    .file-item {
        display: flex;
        align-items: center;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 10px;
    }
    .file-icon {
        font-size: 1.5rem;
        margin-right: 10px;
        color: #6c757d;
    }
    .file-name {
        flex-grow: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .file-size {
        color: #6c757d;
        margin-right: 10px;
    }
    .answer-content {
        white-space: pre-line;
        font-size: 1rem;
        line-height: 1.5;
    }
    .model-config {
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    .prompt-btn {
        margin-right: 8px;
        margin-bottom: 8px;
    }
    .result-item {
        cursor: pointer;
        transition: background-color 0.2s;
    }
    .result-item:hover {
        background-color: #f8f9fa;
    }
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }
    .loading-spinner {
        width: 100px;
        height: 100px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-3">
        <div class="col">
            <h1>AI问股</h1>
            <p class="text-muted">上传相关资料，让AI帮您分析股票</p>
        </div>
    </div>

    <ul class="nav nav-tabs" id="aiStockTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="analyze-tab" data-bs-toggle="tab" data-bs-target="#analyze" type="button" role="tab" aria-controls="analyze" aria-selected="true">分析</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab" aria-controls="history" aria-selected="false">历史记录</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab" aria-controls="settings" aria-selected="false">设置</button>
        </li>
    </ul>
    
    <div class="tab-content p-3 border border-top-0 rounded-bottom" id="aiStockTabContent">
        <!-- 分析 Tab -->
        <div class="tab-pane fade show active" id="analyze" role="tabpanel" aria-labelledby="analyze-tab">
            <div class="row">
                <!-- 左侧上传和提问区域 -->
                <div class="col-lg-5">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>上传资料</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="fileUpload" class="form-label">选择文件</label>
                                <input class="form-control" type="file" id="fileUpload" multiple>
                                <div class="form-text">支持多种格式的文件：PDF、Word、Excel、文本文件等</div>
                            </div>
                            <button class="btn btn-primary" id="uploadBtn">
                                <i class="bi bi-cloud-arrow-up"></i> 上传文件
                            </button>
                            
                            <hr>
                            
                            <div id="fileList" class="mt-3">
                                <p class="text-muted" id="noFilesMsg">尚未上传任何文件</p>
                                <!-- 文件列表将在这里动态显示 -->
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>提问</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="promptTemplate" class="form-label">选择问题模板</label>
                                <div class="d-flex flex-wrap">
                                    {% for key, value in default_prompts.items() %}
                                    <button class="btn btn-outline-secondary prompt-btn" data-prompt="{{ value }}">{{ key }}</button>
                                    {% endfor %}
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="question" class="form-label">问题</label>
                                <textarea class="form-control" id="question" rows="4" placeholder="请输入您的问题，例如：请分析一下这个公司的基本面"></textarea>
                            </div>
                            
                            <button class="btn btn-success" id="analyzeBtn" disabled>
                                <i class="bi bi-search"></i> 开始分析
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧分析结果区域 -->
                <div class="col-lg-7">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5>分析结果</h5>
                            <div>
                                <button class="btn btn-sm btn-outline-primary me-2" id="saveResultBtn" disabled>
                                    <i class="bi bi-bookmark"></i> 保存
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" id="downloadResultBtn" disabled>
                                    <i class="bi bi-download"></i> 下载
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="resultPlaceholder" class="text-center p-5">
                                <i class="bi bi-graph-up" style="font-size: 3rem; color: #ccc;"></i>
                                <p class="mt-3 text-muted">上传文件并提问，获取AI分析结果</p>
                            </div>
                            
                            <div id="analysisResult" style="display: none;">
                                <h4 id="resultQuestion" class="mb-3"></h4>
                                <hr>
                                <div id="resultAnswer" class="answer-content mt-3"></div>
                                
                                <hr>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <span class="text-muted">分析文件：</span>
                                        <span id="resultFiles"></span>
                                    </div>
                                    <div>
                                        <span class="text-muted">分析时间：</span>
                                        <span id="resultTime"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 历史记录 Tab -->
        <div class="tab-pane fade" id="history" role="tabpanel" aria-labelledby="history-tab">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>问题</th>
                            <th>文件</th>
                            <th>分析时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="historyTable">
                        <!-- 历史记录将在这里动态显示 -->
                    </tbody>
                </table>
                <div id="noHistoryMsg" class="text-center p-5">
                    <p class="text-muted">暂无历史记录</p>
                </div>
            </div>
        </div>
        
        <!-- 设置 Tab -->
        <div class="tab-pane fade" id="settings" role="tabpanel" aria-labelledby="settings-tab">
            <div class="model-config">
                <h5>AI模型配置</h5>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="modelType" class="form-label">模型类型</label>
                            <select class="form-select" id="modelType">
                                <option value="openai">OpenAI</option>
                                <option value="claude">Anthropic Claude</option>
                                <option value="gemini" selected>Google Gemini</option>
                                <option value="dify">Dify</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="modelName" class="form-label">模型名称</label>
                            <input type="text" class="form-control" id="modelName" placeholder="例如：gpt-4">
                            <div class="form-text">不同模型类型的默认模型将自动设置，如无特殊需求可留空</div>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="apiKey" class="form-label">API密钥</label>
                    <input type="password" class="form-control" id="apiKey">
                    <div class="form-text">您的API密钥将安全地存储在浏览器本地，不会上传到服务器。如果为空，将使用服务器环境变量中的配置</div>
                </div>
                
                <div class="mb-3">
                    <label for="apiBase" class="form-label">API基础URL</label>
                    <input type="text" class="form-control" id="apiBase" placeholder="例如：https://api.openai.com/v1">
                    <div class="form-text">如使用代理服务或自定义端点，请在此填写API基础URL</div>
                </div>
                
                <div class="mb-3">
                    <label for="proxy" class="form-label">代理地址</label>
                    <input type="text" class="form-control" id="proxy" placeholder="例如：http://127.0.0.1:7890">
                    <div class="form-text">如需使用代理访问API，请在此填写代理地址</div>
                </div>
                
                <div class="d-flex justify-content-end mt-3">
                    <button class="btn btn-primary" id="saveConfigBtn">保存配置</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">删除确认</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除这条分析记录吗？此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 加载中遮罩 -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="spinner-border text-light loading-spinner" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化变量
        const uploadedFiles = [];
        let currentResult = null;
        
        // DOM元素
        const fileUpload = document.getElementById('fileUpload');
        const uploadBtn = document.getElementById('uploadBtn');
        const fileList = document.getElementById('fileList');
        const noFilesMsg = document.getElementById('noFilesMsg');
        const question = document.getElementById('question');
        const analyzeBtn = document.getElementById('analyzeBtn');
        const promptBtns = document.querySelectorAll('.prompt-btn');
        const resultPlaceholder = document.getElementById('resultPlaceholder');
        const analysisResult = document.getElementById('analysisResult');
        const resultQuestion = document.getElementById('resultQuestion');
        const resultAnswer = document.getElementById('resultAnswer');
        const resultFiles = document.getElementById('resultFiles');
        const resultTime = document.getElementById('resultTime');
        const saveResultBtn = document.getElementById('saveResultBtn');
        const downloadResultBtn = document.getElementById('downloadResultBtn');
        const historyTable = document.getElementById('historyTable');
        const noHistoryMsg = document.getElementById('noHistoryMsg');
        const loadingOverlay = document.getElementById('loadingOverlay');
        
        // 模型配置相关
        const modelType = document.getElementById('modelType');
        const modelName = document.getElementById('modelName');
        const apiKey = document.getElementById('apiKey');
        const apiBase = document.getElementById('apiBase');
        const proxy = document.getElementById('proxy');
        const saveConfigBtn = document.getElementById('saveConfigBtn');
        
        // 删除确认模态框
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        let deleteResultId = null;
        
        // 加载历史记录
        loadHistoryResults();
        
        // 加载保存的配置
        loadSavedConfig();
        
        // 问题模板按钮点击事件
        promptBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                question.value = btn.getAttribute('data-prompt');
                updateAnalyzeButton();
            });
        });
        
        // 上传按钮点击事件
        uploadBtn.addEventListener('click', async () => {
            if (fileUpload.files.length === 0) {
                alert('请先选择文件');
                return;
            }
            
            const formData = new FormData();
            for (const file of fileUpload.files) {
                formData.append('files', file);
            }
            
            showLoading();
            
            try {
                const response = await fetch('/ai_stock/api/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.status === 'success') {
                    // 添加到已上传文件列表
                    result.data.forEach(file => {
                        uploadedFiles.push(file);
                    });
                    
                    // 更新文件列表显示
                    updateFileList();
                    
                    // 清空文件输入框
                    fileUpload.value = '';
                    
                    // 更新分析按钮状态
                    updateAnalyzeButton();
                } else {
                    alert('上传失败: ' + result.message);
                }
            } catch (error) {
                console.error('上传文件出错:', error);
                alert('上传文件时出错');
            } finally {
                hideLoading();
            }
        });
        
        // 分析按钮点击事件
        analyzeBtn.addEventListener('click', async () => {
            if (uploadedFiles.length === 0) {
                alert('请先上传文件');
                return;
            }
            
            if (!question.value.trim()) {
                alert('请输入问题');
                return;
            }
            
            // 获取模型配置
            const modelConfig = getSavedConfig();
            // 允许服务器端使用环境变量中的API密钥
            /* if (!modelConfig.api_key) {
                alert('请先在设置中配置API密钥或在服务器环境变量中配置');
                return;
            } */
            
            showLoading();
            
            try {
                console.log('开始分析请求，上传文件IDs:', uploadedFiles.map(file => file.id));
                
                const requestData = {
                    question: question.value,
                    file_ids: uploadedFiles.map(file => file.id),
                    ai_model_config: modelConfig
                };
                
                console.log('发送请求数据:', JSON.stringify(requestData));
                
                const response = await fetch('/ai_stock/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (!response.ok) {
                    throw new Error(`服务器返回错误状态码: ${response.status}`);
                }
                
                const result = await response.json();
                console.log('接收到分析结果:', result);
                
                if (result.status === 'success') {
                    currentResult = result.data;
                    displayResult(currentResult);
                    
                    // 刷新历史记录
                    loadHistoryResults();
                    
                    // 清空问题，方便下次输入
                    // question.value = '';
                } else {
                    alert('分析失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                console.error('分析股票出错:', error);
                alert('分析股票时出错: ' + error.message);
            } finally {
                hideLoading();
            }
        });
        
        // 保存结果按钮点击事件
        saveResultBtn.addEventListener('click', () => {
            alert('分析结果已自动保存，可在历史记录中查看');
        });
        
        // 下载结果按钮点击事件
        downloadResultBtn.addEventListener('click', () => {
            if (!currentResult) return;
            
            const content = `
问题：${currentResult.question}

分析结果：
${currentResult.answer}

分析文件：${currentResult.file_names.join(', ')}
分析时间：${currentResult.created_at}
            `.trim();
            
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `AI问股分析_${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });
        
        // 问题输入框变化事件
        question.addEventListener('input', updateAnalyzeButton);
        
        // 保存配置按钮点击事件
        saveConfigBtn.addEventListener('click', () => {
            const config = {
                model_type: modelType.value,
                model_name: modelName.value,
                api_key: apiKey.value,
                api_base: apiBase.value,
                proxy: proxy.value
            };
            
            localStorage.setItem('aiStockConfig', JSON.stringify(config));
            alert('配置已保存');
        });
        
        // 标签页切换事件
        const historyTab = document.getElementById('history-tab');
        historyTab.addEventListener('shown.bs.tab', () => {
            loadHistoryResults();
        });
        
        // 更新分析按钮状态
        function updateAnalyzeButton() {
            analyzeBtn.disabled = uploadedFiles.length === 0 || !question.value.trim();
        }
        
        // 更新文件列表显示
        function updateFileList() {
            if (uploadedFiles.length === 0) {
                noFilesMsg.style.display = 'block';
                fileList.innerHTML = '';
                return;
            }
            
            noFilesMsg.style.display = 'none';
            
            let html = '';
            uploadedFiles.forEach((file, index) => {
                html += `
                <div class="file-item">
                    <div class="file-icon">
                        <i class="bi bi-file-text"></i>
                    </div>
                    <div class="file-name">${file.name}</div>
                    <div class="file-size">${formatFileSize(file.size)}</div>
                    <button class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
                `;
            });
            
            fileList.innerHTML = html;
        }
        
        // 移除文件
        window.removeFile = function(index) {
            uploadedFiles.splice(index, 1);
            updateFileList();
            updateAnalyzeButton();
        };
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
            return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
        }
        
        // 显示分析结果
        function displayResult(result) {
            resultQuestion.textContent = result.question;
            resultAnswer.textContent = result.answer;
            resultFiles.textContent = result.file_names.join(', ');
            resultTime.textContent = result.created_at;
            
            resultPlaceholder.style.display = 'none';
            analysisResult.style.display = 'block';
            saveResultBtn.disabled = false;
            downloadResultBtn.disabled = false;
        }
        
        // 加载历史记录
        async function loadHistoryResults() {
            try {
                const response = await fetch('/ai_stock/api/results');
                const result = await response.json();
                
                if (result.status === 'success' && result.data.length > 0) {
                    noHistoryMsg.style.display = 'none';
                    
                    let html = '';
                    result.data.forEach(item => {
                        html += `
                        <tr class="result-item" data-id="${item.id}">
                            <td>${truncateText(item.question, 50)}</td>
                            <td>${item.file_names.join(', ')}</td>
                            <td>${item.created_at}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-danger" onclick="confirmDelete('${item.id}', event)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                        `;
                    });
                    
                    historyTable.innerHTML = html;
                    
                    // 添加行点击事件
                    document.querySelectorAll('.result-item').forEach(row => {
                        row.addEventListener('click', async (event) => {
                            if (event.target.tagName !== 'BUTTON' && !event.target.closest('button')) {
                                const id = row.getAttribute('data-id');
                                await loadResult(id);
                                
                                // 切换到分析选项卡
                                document.getElementById('analyze-tab').click();
                            }
                        });
                    });
                } else {
                    historyTable.innerHTML = '';
                    noHistoryMsg.style.display = 'block';
                }
            } catch (error) {
                console.error('加载历史记录出错:', error);
                alert('加载历史记录时出错');
            }
        }
        
        // 加载单个分析结果
        async function loadResult(id) {
            showLoading();
            
            try {
                const response = await fetch(`/ai_stock/api/results/${id}`);
                const result = await response.json();
                
                if (result.status === 'success') {
                    currentResult = result.data;
                    displayResult(currentResult);
                } else {
                    alert('获取分析结果失败: ' + result.message);
                }
            } catch (error) {
                console.error('加载分析结果出错:', error);
                alert('加载分析结果时出错');
            } finally {
                hideLoading();
            }
        }
        
        // 确认删除
        window.confirmDelete = function(id, event) {
            event.stopPropagation();
            deleteResultId = id;
            deleteModal.show();
        };
        
        // 删除分析结果
        confirmDeleteBtn.addEventListener('click', async () => {
            if (!deleteResultId) return;
            
            showLoading();
            
            try {
                const response = await fetch(`/ai_stock/api/results/${deleteResultId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.status === 'success') {
                    deleteModal.hide();
                    loadHistoryResults();
                } else {
                    alert('删除分析结果失败: ' + result.message);
                }
            } catch (error) {
                console.error('删除分析结果出错:', error);
                alert('删除分析结果时出错');
            } finally {
                hideLoading();
                deleteResultId = null;
            }
        });
        
        // 加载保存的配置
        function loadSavedConfig() {
            const config = getSavedConfig();
            
            modelType.value = config.model_type || 'gemini';
            modelName.value = config.model_name || 'gemini-2.0-flash';
            apiKey.value = config.api_key || '';
            apiBase.value = config.api_base || '';
            proxy.value = config.proxy || '';
        }
        
        // 获取保存的配置
        function getSavedConfig() {
            const defaultConfig = {
                model_type: 'gemini',
                api_key: '',
                api_base: '',
                proxy: '',
                model_name: 'gemini-2.0-flash'
            };
            
            try {
                const saved = localStorage.getItem('aiStockConfig');
                return saved ? JSON.parse(saved) : defaultConfig;
            } catch (error) {
                console.error('获取配置出错:', error);
                return defaultConfig;
            }
        }
        
        // 截断文本
        function truncateText(text, maxLength) {
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }
        
        // 显示加载中遮罩
        function showLoading() {
            loadingOverlay.style.display = 'flex';
        }
        
        // 隐藏加载中遮罩
        function hideLoading() {
            loadingOverlay.style.display = 'none';
        }
    });
</script>
{% endblock %} 